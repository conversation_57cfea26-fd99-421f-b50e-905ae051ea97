{% extends 'KLC_App/admin_panel/base.html' %}

{% block title %}إدارة المشرفين{% endblock %}
{% block page_title %}إدارة المشرفين{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="flex items-center justify-between mb-6">
    <div>
        <h1 class="text-2xl font-bold text-gray-900">إدارة المشرفين</h1>
        <p class="text-gray-600">إنشاء وتعديل وإدارة حسابات المشرفين</p>
    </div>
    <button onclick="openCreateAdminModal()" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 space-x-reverse transition-colors">
        <i class="fas fa-plus"></i>
        <span>إضافة مشرف جديد</span>
    </button>
</div>

<!-- <PERSON><PERSON> List -->
<div class="bg-white rounded-xl card-shadow overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">قائمة المشرفين</h2>
    </div>
    
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المشرف</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الصلاحيات</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">آخر تسجيل دخول</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for admin in admin_profiles %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-primary-600"></i>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900">
                                    {{ admin.user.get_full_name|default:admin.user.username }}
                                </div>
                                <div class="text-sm text-gray-500">{{ admin.user.email }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm text-gray-900">
                            {% if admin.full_permissions %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    صلاحيات كاملة
                                </span>
                            {% else %}
                                <div class="flex flex-wrap gap-1">
                                    {% if admin.can_manage_halls %}
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">القاعات</span>
                                    {% endif %}
                                    {% if admin.can_manage_users %}
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">المستخدمين</span>
                                    {% endif %}
                                    {% if admin.can_approve_reservations %}
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800">الحجوزات</span>
                                    {% endif %}
                                    {% if admin.can_manage_transactions %}
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-indigo-100 text-indigo-800">المعاملات</span>
                                    {% endif %}
                                    {% if admin.can_view_reports %}
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-pink-100 text-pink-800">التقارير</span>
                                    {% endif %}
                                    {% if admin.can_manage_news %}
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-100 text-orange-800">الأخبار</span>
                                    {% endif %}
                                    {% if admin.can_handle_complaints %}
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">الشكاوي</span>
                                    {% endif %}
                                </div>
                            {% endif %}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {% if admin.user.last_login %}
                            {{ admin.user.last_login|date:"d/m/Y H:i" }}
                        {% else %}
                            لم يسجل دخول بعد
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ admin.created_at|date:"d/m/Y" }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button onclick="editAdmin({{ admin.id }})" class="text-primary-600 hover:text-primary-900">
                                <i class="fas fa-edit"></i>
                            </button>
                            {% if admin.user != request.user %}
                            <button onclick="confirmDeactivateAdmin({{ admin.id }}, '{{ admin.user.username }}')" class="text-red-600 hover:text-red-900">
                                <i class="fas fa-user-slash"></i>
                            </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                        لا توجد حسابات مشرفين
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Create Admin Modal -->
<div id="createAdminModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-xl max-w-2xl w-full max-h-screen overflow-y-auto">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">إضافة مشرف جديد</h3>
                <button onclick="closeCreateAdminModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <form method="post" class="space-y-6">
                {% csrf_token %}
                <input type="hidden" name="action" value="create_admin">
                
                <!-- Basic Info -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
                        <input type="text" name="username" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                        <input type="email" name="email" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الأول</label>
                        <input type="text" name="first_name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الأخير</label>
                        <input type="text" name="last_name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                    <input type="password" name="password" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                </div>
                
                <!-- Permissions -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-4">الصلاحيات</label>
                    
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" name="full_permissions" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50">
                            <span class="mr-2 text-sm text-gray-700 font-medium">صلاحيات كاملة (جميع الصلاحيات)</span>
                        </label>
                        
                        <div id="specificPermissions" class="grid grid-cols-1 md:grid-cols-2 gap-3 mt-4">
                            <label class="flex items-center">
                                <input type="checkbox" name="can_manage_halls" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm text-gray-700">إدارة القاعات</span>
                            </label>
                            
                            <label class="flex items-center">
                                <input type="checkbox" name="can_manage_users" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm text-gray-700">إدارة المستخدمين</span>
                            </label>
                            
                            <label class="flex items-center">
                                <input type="checkbox" name="can_approve_reservations" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm text-gray-700">الموافقة على الحجوزات</span>
                            </label>
                            
                            <label class="flex items-center">
                                <input type="checkbox" name="can_manage_transactions" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm text-gray-700">إدارة المعاملات</span>
                            </label>
                            
                            <label class="flex items-center">
                                <input type="checkbox" name="can_view_reports" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm text-gray-700">عرض التقارير</span>
                            </label>
                            
                            <label class="flex items-center">
                                <input type="checkbox" name="can_manage_admins" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm text-gray-700">إدارة المشرفين</span>
                            </label>
                            
                            <label class="flex items-center">
                                <input type="checkbox" name="can_manage_news" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm text-gray-700">إدارة الأخبار</span>
                            </label>
                            
                            <label class="flex items-center">
                                <input type="checkbox" name="can_handle_complaints" class="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm text-gray-700">معالجة الشكاوي</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 space-x-reverse pt-6 border-t border-gray-200">
                    <button type="button" onclick="closeCreateAdminModal()" class="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors">
                        إنشاء المشرف
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function openCreateAdminModal() {
        document.getElementById('createAdminModal').classList.remove('hidden');
        document.body.classList.add('overflow-hidden');
    }
    
    function closeCreateAdminModal() {
        document.getElementById('createAdminModal').classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
    }
    
    function editAdmin(adminId) {
        // TODO: Implement edit functionality
        alert('تحرير المشرف - قيد التطوير');
    }
    
    function confirmDeactivateAdmin(adminId, username) {
        if (confirm(`هل أنت متأكد من إلغاء تفعيل المشرف "${username}"؟`)) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                {% csrf_token %}
                <input type="hidden" name="action" value="deactivate_admin">
                <input type="hidden" name="admin_id" value="${adminId}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    // Handle full permissions checkbox
    document.querySelector('input[name="full_permissions"]').addEventListener('change', function() {
        const specificPermissions = document.getElementById('specificPermissions');
        const checkboxes = specificPermissions.querySelectorAll('input[type="checkbox"]');
        
        if (this.checked) {
            specificPermissions.style.opacity = '0.5';
            checkboxes.forEach(cb => cb.disabled = true);
        } else {
            specificPermissions.style.opacity = '1';
            checkboxes.forEach(cb => cb.disabled = false);
        }
    });
    
    // Close modal on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeCreateAdminModal();
        }
    });
</script>
{% endblock %}
