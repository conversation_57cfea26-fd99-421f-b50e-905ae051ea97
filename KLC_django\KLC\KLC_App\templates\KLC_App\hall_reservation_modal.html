<!-- Hall Reservation Modal -->
<!-- Flatpickr CSS -->
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css"
/>

<!-- Flatpickr Theme  -->
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/dark.css"
/>
<div
  class="modal fade"
  id="hallReservationModal"
  tabindex="-1"
  aria-labelledby="hallReservationModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-dark text-white">
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
        <h5 class="modal-title" id="hallReservationModalLabel">
          إحجز قاعة المجلس
        </h5>
      </div>
      <div class="modal-body">
        <form
          class="needs-validation text-center"
          action="{% url 'reserve_hall' %}"
          method="POST"
          id="hall-reservation-form"
        >
          {% csrf_token %}
          <!-- Form content for service 2 -->
          <div class="mb-3">
            <label for="hallReservationFullName" class="form-label"
              >الإسم الرباعي (حسب الهوية)</label
            >
            <input
              type="text"
              class="form-control"
              id="hallReservationFullName"
              name="hallReservationFullName"
              required
              pattern="^[\u0621-\u064A]+( [\u0621-\u064A]+){3,5}$"
              title="الإسم الرباعي يجب أن يتكون من 4 مقاطع باللغة العربية فقط"
              oninput="this.value = this.value.replace(/[^ء-ي ]/g, '')"
              placeholder="الإسم الرباعي يجب أن يتكون من 4 مقاطع باللغة العربية فقط"
            />
          </div>
          <div class="mb-3">
            <label for="hallReservationPhoneNumber" class="form-label"
              >رقم الهاتف</label
            >
            <input
              type="tel"
              class="form-control telefone-input"
              id="hallReservationPhoneNumber"
              name="hallReservationPhoneNumber"
              required
              placeholder="مثال:0592345678 أو مع رقم مفتاح الدولة"
              minlength="10"
              maxlength="15"
              oninput="this.value = this.value.replace(/[^0-9+]/g, '')"
              pattern="\d{10, 15}"
              title="رقم الهاتف يجب أن يتكون من أرقام فقط"
            />
          </div>
          <div class="mb-3">
            <label for="hallReservationEventType" class="form-label"
              >نوع المناسبة</label
            >
            <select
              class="form-select"
              id="hallReservationEventType"
              name="hallReservationEventType"
              required
            >
              <option value="" disabled selected hidden>
                إختر نوع المناسبة
              </option>
              <option value="عرس">عرس</option>
              <option value="طلبة عريس للرجال">طلبة عريس للرجال</option>
              <option value="خطوبة للنساء">خطوبة للنساء</option>
              <option value="حفل حناء عروس">حفل حناء عروس</option>
              <option value="حفل تخرج (توجيهي أو جامعة)">
                حفل تخرج (توجيهي أو جامعة)
              </option>
              <option value="غداء عرس">غداء عرس</option>
              <option value="ورشات عمل">ورشات عمل</option>
              <option value="بيوت أجر">بيوت أجر</option>
            </select>
          </div>
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="startDateInput" class="form-label">تاريخ البدء</label>
              <input
                type="text"
                class="form-control flatpickr-date"
                name="startDateInput"
                id="startDateInput"
                placeholder="اختر تاريخ البدء"
                autocomplete="off"
                required
              />
            </div>

            <div class="col-md-6 mb-3">
              <label for="endDateInput" class="form-label"
                >تاريخ الإنتهاء</label
              >
              <input
                type="text"
                class="form-control flatpickr-date"
                name="endDateInput"
                id="endDateInput"
                placeholder="اختر تاريخ الإنتهاء"
                autocomplete="off"
                required
                disabled
              />
            </div>
          </div>
          <div class="mb-3">
            <p class="text-danger">
              * تنويه بأن هذا الحجز مؤقت لمدة ثلاثة أيام ويجب التوجه إلى المجلس
              لتثبيته
            </p>
          </div>
          <!--Get the council contact info text-->
          {% include 'KLC_App/council_contact_info_for_services.html' %}
          <button type="submit" class="btn btn-dark w-100">إرسال الطلب</button>
        </form>
      </div>
    </div>
  </div>
</div>
<!-- End of Hall Reservation Modal -->
<!-- Flatpickr JS -->
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

<!-- Flatpickr Arabic Language -->
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
<!-- The Js For Date picker -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Calculate dates
    const today = new Date();

    // Start date should be at least 4 days in the future
    const minSelectableDate = new Date();
    minSelectableDate.setDate(today.getDate() + 3);

    // Calculate end of year for max date
    const currentYear = today.getFullYear();
    const nextYear = today.getMonth() > 10 ? currentYear + 1 : currentYear;
    const endOfYear = new Date(nextYear, 11, 31);

    // Common flatpickr config options
    const commonConfig = {
      dateFormat: "Y-m-d", // YYYY-MM-DD format for backend compatibility
      locale: "ar",
      disableMobile: true,
      static: false,
      monthSelectorType: "dropdown",
      showMonths: 1,
      position: "auto",
      ariaDateFormat: "Y-m-d",
      animate: true,
      showDaysInNextAndPreviousMonths: true,
      fixedHeight: true,
      prevArrow: '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"></path></svg>',
      nextArrow: '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path></svg>'
    };

    // Initialize start date picker
    const startDatePicker = flatpickr("#startDateInput", {
      ...commonConfig,
      minDate: minSelectableDate,
      maxDate: endOfYear,
      onChange: function (selectedDates, dateStr) {
        if (selectedDates.length > 0) {
          // Enable end date picker once start date is selected
          const endDateInput = document.getElementById('endDateInput');
          endDateInput.disabled = false;
          endDateInput.placeholder = "اختر تاريخ الإنتهاء";

          // Set min/max range for end date (start date to start date + 7 days)
          const startDate = selectedDates[0];
          const minEndDate = new Date(startDate);
          const maxEndDate = new Date(startDate);
          maxEndDate.setDate(startDate.getDate() + 7);

          // Ensure max end date doesn't exceed end of year
          if (maxEndDate > endOfYear) {
            maxEndDate.setTime(endOfYear.getTime());
          }

          // Update end date picker constraints
          endDatePicker.set('minDate', minEndDate);
          endDatePicker.set('maxDate', maxEndDate);
          endDatePicker.clear(); // Clear any previously selected end date

          // Open the end date picker automatically after a short delay
          setTimeout(() => {
            endDatePicker.open();
          }, 300);
        }
      }
    });

    // Initialize end date picker
    const endDatePicker = flatpickr("#endDateInput", {
      ...commonConfig,
      onOpen: function () {
        // If no start date is selected, close the end date picker
        if (!startDatePicker.selectedDates.length) {
          this.close();
          return;
        }
      }
    });

    // Form validation for date fields
    const form = document.getElementById('hall-reservation-form');
    if (form) {
      form.addEventListener('submit', function (e) {
        const startDateInput = document.getElementById('startDateInput');
        const endDateInput = document.getElementById('endDateInput');

        // Validate dates before submission
        if (!startDateInput.value) {
          e.preventDefault();
          alert('الرجاء اختيار تاريخ البدء');
          return;
        }

        if (!endDateInput.value) {
          e.preventDefault();
          alert('الرجاء اختيار تاريخ الإنتهاء');
          return;
        }
      });
    }
  });
</script>
