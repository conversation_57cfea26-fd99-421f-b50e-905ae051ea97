{% extends "admin/base.html" %}
{% load static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
/* Performance optimizations - Remove heavy animations and transitions */
* {
    transition: none !important;
    animation: none !important;
}

/* RTL Support for Arabic */
body {
    direction: rtl;
    text-align: right;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Fast loading optimizations */
.module {
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Responsive design improvements */
@media (max-width: 768px) {
    .dashboard .module {
        width: 100% !important;
        float: none !important;
        margin: 10px 0 !important;
    }
    
    .results {
        overflow-x: auto;
    }
    
    .results table {
        min-width: 600px;
    }
}

/* Custom styling for KLC branding */
#header {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
    color: white;
}

#branding h1 {
    color: white;
    font-weight: 600;
}

#branding h1 a:link, #branding h1 a:visited {
    color: white;
}

/* Improve form styling */
.form-row {
    margin-bottom: 15px;
}

.form-row label {
    font-weight: 600;
    color: #333;
}

/* Status indicators styling */
.status-synced {
    color: #28a745;
    font-weight: bold;
}

.status-pending {
    color: #ffc107;
    font-weight: bold;
}

.status-error {
    color: #dc3545;
    font-weight: bold;
}

/* Action buttons styling */
.button, input[type=submit], input[type=button], .submit-row input, a.button {
    background: #2c5aa0;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    color: white;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
}

.button:hover, input[type=submit]:hover, input[type=button]:hover, .submit-row input:hover, a.button:hover {
    background: #1e3a8a;
}

.button.default, input[type=submit].default, .submit-row input.default {
    background: #28a745;
}

.button.default:hover, input[type=submit].default:hover, .submit-row input.default:hover {
    background: #1e7e34;
}

/* Table improvements */
.results th {
    background: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.results tr:nth-child(even) {
    background: #f8f9fa;
}

.results tr:hover {
    background: #e9ecef;
}

/* Sidebar optimizations */
.module h2, .module caption, .inline-group h2 {
    background: #2c5aa0;
    color: white;
    font-weight: 600;
    padding: 10px 15px;
    margin: 0;
    border-radius: 4px 4px 0 0;
}

/* Loading indicator */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2c5aa0;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Hide unnecessary elements for performance */
.dashboard .module table {
    width: 100%;
}

/* Optimize change list */
.change-list .results {
    overflow-x: auto;
}

/* Custom success/error messages */
.messagelist .success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.messagelist .error {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.messagelist .warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

/* Optimize filters */
#changelist-filter {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 15px;
}

#changelist-filter h3 {
    color: #2c5aa0;
    font-weight: 600;
    margin-bottom: 10px;
}

/* Print styles */
@media print {
    #header, #footer, .breadcrumbs, .object-tools, .submit-row {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}
</style>
{% endblock %}

{% block branding %}
<h1 id="site-name">
    <a href="{% url 'admin:index' %}">
        نظام إدارة مجلس قروي كفر عين
    </a>
</h1>
{% endblock %}

{% block nav-global %}{% endblock %}
