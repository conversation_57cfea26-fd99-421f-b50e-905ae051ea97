"""
Django management command to migrate existing custom admin system to Django admin
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from KLC_App.models import Person, HallReservation, Transaction, SuggestionComplaint, NewsAchievement
from KLC_App.firebase_sync import FirebaseSyncService
import bcrypt
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Migrate existing custom admin system to Django admin'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-superuser',
            action='store_true',
            help='Create Django superuser accounts for existing admins',
        )
        parser.add_argument(
            '--sync-data',
            action='store_true',
            help='Sync all data from Firebase before migration',
        )
        parser.add_argument(
            '--create-groups',
            action='store_true',
            help='Create admin groups with appropriate permissions',
        )
        parser.add_argument(
            '--admin-username',
            type=str,
            help='Username for the main admin account',
            default='admin'
        )
        parser.add_argument(
            '--admin-password',
            type=str,
            help='Password for the main admin account',
            default='klc@admin2024'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("Starting migration to Django admin..."))
        
        try:
            with transaction.atomic():
                if options['sync_data']:
                    self._sync_firebase_data()
                
                if options['create_groups']:
                    self._create_admin_groups()
                
                if options['create_superuser']:
                    self._create_admin_users(options['admin_username'], options['admin_password'])
                
                self._update_settings()
                
            self.stdout.write(self.style.SUCCESS("✅ Migration completed successfully!"))
            self._print_next_steps()
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Migration failed: {str(e)}"))
            logger.error(f"Migration failed: {str(e)}", exc_info=True)
            raise CommandError(f"Migration failed: {str(e)}")

    def _sync_firebase_data(self):
        """Sync all data from Firebase to SQLite"""
        self.stdout.write("Syncing data from Firebase...")
        
        sync_service = FirebaseSyncService()
        collections = ['persons', 'hall_reservations', 'transactions', 'suggestions_complaints', 'news_achievements']
        
        for collection in collections:
            try:
                self.stdout.write(f"  Syncing {collection}...")
                sync_log = sync_service.sync_collection_from_firebase(collection)
                
                if sync_log.sync_status == 'completed':
                    self.stdout.write(
                        self.style.SUCCESS(f"    ✅ {sync_log.records_successful} records synced")
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(
                            f"    ⚠️ {sync_log.records_successful} successful, "
                            f"{sync_log.records_failed} failed"
                        )
                    )
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"    ❌ Failed to sync {collection}: {str(e)}"))

    def _create_admin_groups(self):
        """Create admin groups with appropriate permissions"""
        self.stdout.write("Creating admin groups...")
        
        # Create KLC Admin group
        admin_group, created = Group.objects.get_or_create(name='KLC Admin')
        if created:
            self.stdout.write("  ✅ Created 'KLC Admin' group")
        
        # Create KLC Manager group (with more permissions)
        manager_group, created = Group.objects.get_or_create(name='KLC Manager')
        if created:
            self.stdout.write("  ✅ Created 'KLC Manager' group")
        
        # Get content types for our models
        person_ct = ContentType.objects.get_for_model(Person)
        reservation_ct = ContentType.objects.get_for_model(HallReservation)
        transaction_ct = ContentType.objects.get_for_model(Transaction)
        suggestion_ct = ContentType.objects.get_for_model(SuggestionComplaint)
        news_ct = ContentType.objects.get_for_model(NewsAchievement)
        
        # Admin permissions (basic CRUD)
        admin_permissions = [
            # Person permissions
            Permission.objects.get(codename='view_person', content_type=person_ct),
            Permission.objects.get(codename='change_person', content_type=person_ct),
            
            # Reservation permissions
            Permission.objects.get(codename='view_hallreservation', content_type=reservation_ct),
            Permission.objects.get(codename='add_hallreservation', content_type=reservation_ct),
            Permission.objects.get(codename='change_hallreservation', content_type=reservation_ct),
            
            # Transaction permissions
            Permission.objects.get(codename='view_transaction', content_type=transaction_ct),
            Permission.objects.get(codename='add_transaction', content_type=transaction_ct),
            Permission.objects.get(codename='change_transaction', content_type=transaction_ct),
            
            # Suggestion permissions
            Permission.objects.get(codename='view_suggestioncomplaint', content_type=suggestion_ct),
            Permission.objects.get(codename='delete_suggestioncomplaint', content_type=suggestion_ct),
            
            # News permissions
            Permission.objects.get(codename='view_newsachievement', content_type=news_ct),
            Permission.objects.get(codename='add_newsachievement', content_type=news_ct),
            Permission.objects.get(codename='change_newsachievement', content_type=news_ct),
        ]
        
        # Manager permissions (includes delete permissions)
        manager_permissions = admin_permissions + [
            Permission.objects.get(codename='add_person', content_type=person_ct),
            Permission.objects.get(codename='delete_person', content_type=person_ct),
            Permission.objects.get(codename='delete_hallreservation', content_type=reservation_ct),
            Permission.objects.get(codename='delete_transaction', content_type=transaction_ct),
            Permission.objects.get(codename='delete_newsachievement', content_type=news_ct),
        ]
        
        # Assign permissions
        admin_group.permissions.set(admin_permissions)
        manager_group.permissions.set(manager_permissions)
        
        self.stdout.write("  ✅ Assigned permissions to groups")

    def _create_admin_users(self, admin_username, admin_password):
        """Create Django admin users based on existing Firebase admin accounts"""
        self.stdout.write("Creating admin users...")
        
        # Create main superuser
        if not User.objects.filter(username=admin_username).exists():
            superuser = User.objects.create_superuser(
                username=admin_username,
                email=f'{admin_username}@klc.local',
                password=admin_password,
                first_name='KLC',
                last_name='Administrator'
            )
            self.stdout.write(f"  ✅ Created superuser: {admin_username}")
        else:
            self.stdout.write(f"  ⚠️ Superuser {admin_username} already exists")
        
        # Create users for existing admins from the hardcoded list
        existing_admins = ["ahmad", "abood", "rami"]
        
        for admin_name in existing_admins:
            if not User.objects.filter(username=admin_name).exists():
                user = User.objects.create_user(
                    username=admin_name,
                    email=f'{admin_name}@klc.local',
                    password='klc@admin2024',  # Default password, should be changed
                    first_name=admin_name.title(),
                    last_name='KLC Admin',
                    is_staff=True,
                    is_active=True
                )
                
                # Assign to appropriate group
                if admin_name == 'ahmad':  # Main admin gets manager permissions
                    manager_group = Group.objects.get(name='KLC Manager')
                    user.groups.add(manager_group)
                else:
                    admin_group = Group.objects.get(name='KLC Admin')
                    user.groups.add(admin_group)
                
                self.stdout.write(f"  ✅ Created admin user: {admin_name}")
            else:
                self.stdout.write(f"  ⚠️ Admin user {admin_name} already exists")

    def _update_settings(self):
        """Update Django settings for admin interface"""
        self.stdout.write("Updating settings...")
        
        # This would typically involve updating settings.py programmatically
        # For now, we'll just print what needs to be done
        self.stdout.write("  ℹ️ Settings updates needed (manual):")
        self.stdout.write("    - Ensure 'django.contrib.admin' is in INSTALLED_APPS")
        self.stdout.write("    - Update URL patterns to include admin URLs")
        self.stdout.write("    - Configure admin site settings")

    def _print_next_steps(self):
        """Print next steps for the user"""
        self.stdout.write("\n" + "="*60)
        self.stdout.write(self.style.SUCCESS("MIGRATION COMPLETED SUCCESSFULLY!"))
        self.stdout.write("="*60)
        
        self.stdout.write("\nNext Steps:")
        self.stdout.write("1. Run migrations: python manage.py migrate")
        self.stdout.write("2. Collect static files: python manage.py collectstatic")
        self.stdout.write("3. Access Django admin at: /django-admin/")
        self.stdout.write("4. Update URL patterns if needed")
        self.stdout.write("5. Customize admin interface as needed")
        
        self.stdout.write("\nDefault Login Credentials:")
        self.stdout.write("  Superuser: admin / klc@admin2024")
        self.stdout.write("  Admins: ahmad, abood, rami / klc@admin2024")
        
        self.stdout.write("\n⚠️  IMPORTANT: Change default passwords after first login!")
        
        self.stdout.write("\nData Summary:")
        self.stdout.write(f"  Persons: {Person.objects.count()}")
        self.stdout.write(f"  Reservations: {HallReservation.objects.count()}")
        self.stdout.write(f"  Transactions: {Transaction.objects.count()}")
        self.stdout.write(f"  Suggestions: {SuggestionComplaint.objects.count()}")
        self.stdout.write(f"  News: {NewsAchievement.objects.count()}")
