"""
Django management command to sync data between Firebase and SQLite
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import User
from KLC_App.firebase_sync import FirebaseSyncService
from KLC_App.models import FirebaseSyncLog
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Sync data between Firebase Firestore and local SQLite database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--collection',
            type=str,
            help='Specific collection to sync (persons, hall_reservations, transactions, etc.)',
        )
        parser.add_argument(
            '--direction',
            type=str,
            choices=['import', 'export', 'bidirectional'],
            default='import',
            help='Sync direction: import from Firebase, export to Firebase, or bidirectional',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Sync all collections',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be synced without actually syncing',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force sync even if recent sync exists',
        )

    def handle(self, *args, **options):
        sync_service = FirebaseSyncService()
        
        # Get or create a system user for management commands
        system_user, created = User.objects.get_or_create(
            username='system',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'System',
                'last_name': 'User',
                'is_staff': True,
                'is_active': True
            }
        )
        
        collections_to_sync = []
        
        if options['all']:
            collections_to_sync = list(sync_service.collection_model_mapping.keys())
        elif options['collection']:
            if options['collection'] not in sync_service.collection_model_mapping:
                raise CommandError(f"Unknown collection: {options['collection']}")
            collections_to_sync = [options['collection']]
        else:
            # Default to syncing all collections
            collections_to_sync = list(sync_service.collection_model_mapping.keys())
        
        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING(f"DRY RUN: Would sync collections: {', '.join(collections_to_sync)}")
            )
            for collection in collections_to_sync:
                status = sync_service.get_sync_status(collection)
                self.stdout.write(f"  {collection}: {status['total_records']} records")
            return
        
        self.stdout.write(f"Starting sync for collections: {', '.join(collections_to_sync)}")
        
        total_synced = 0
        total_failed = 0
        
        for collection in collections_to_sync:
            try:
                self.stdout.write(f"Syncing {collection}...")
                
                # Check if recent sync exists and force is not used
                if not options['force']:
                    recent_sync = FirebaseSyncLog.objects.filter(
                        collection_name=collection,
                        sync_status='completed'
                    ).order_by('-completed_at').first()
                    
                    if recent_sync and recent_sync.completed_at:
                        from django.utils import timezone
                        from datetime import timedelta
                        
                        if timezone.now() - recent_sync.completed_at < timedelta(hours=1):
                            self.stdout.write(
                                self.style.WARNING(
                                    f"Skipping {collection} - recent sync completed at {recent_sync.completed_at}. "
                                    f"Use --force to override."
                                )
                            )
                            continue
                
                if options['direction'] == 'import':
                    sync_log = sync_service.sync_collection_from_firebase(collection, system_user)
                    
                    if sync_log.sync_status == 'completed':
                        self.stdout.write(
                            self.style.SUCCESS(
                                f"✅ {collection}: {sync_log.records_successful} records synced successfully"
                            )
                        )
                        total_synced += sync_log.records_successful
                    elif sync_log.sync_status == 'partial':
                        self.stdout.write(
                            self.style.WARNING(
                                f"⚠️  {collection}: {sync_log.records_successful} successful, "
                                f"{sync_log.records_failed} failed"
                            )
                        )
                        total_synced += sync_log.records_successful
                        total_failed += sync_log.records_failed
                    else:
                        self.stdout.write(
                            self.style.ERROR(f"❌ {collection}: Sync failed - {sync_log.error_message}")
                        )
                        total_failed += sync_log.records_processed
                
                elif options['direction'] == 'export':
                    # TODO: Implement export functionality
                    self.stdout.write(
                        self.style.WARNING(f"Export functionality not yet implemented for {collection}")
                    )
                
                elif options['direction'] == 'bidirectional':
                    # TODO: Implement bidirectional sync
                    self.stdout.write(
                        self.style.WARNING(f"Bidirectional sync not yet implemented for {collection}")
                    )
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ Error syncing {collection}: {str(e)}")
                )
                logger.error(f"Error syncing {collection}: {str(e)}", exc_info=True)
                total_failed += 1
        
        # Summary
        self.stdout.write("\n" + "="*50)
        self.stdout.write(f"Sync Summary:")
        self.stdout.write(f"  Collections processed: {len(collections_to_sync)}")
        self.stdout.write(f"  Records synced: {total_synced}")
        if total_failed > 0:
            self.stdout.write(self.style.ERROR(f"  Records failed: {total_failed}"))
        else:
            self.stdout.write(self.style.SUCCESS("  All records synced successfully!"))
        
        # Show sync status for all collections
        self.stdout.write("\nCurrent Sync Status:")
        for collection in sync_service.collection_model_mapping.keys():
            status = sync_service.get_sync_status(collection)
            sync_percentage = status['sync_percentage']
            
            if sync_percentage == 100:
                style = self.style.SUCCESS
                icon = "✅"
            elif sync_percentage >= 80:
                style = self.style.WARNING
                icon = "⚠️"
            else:
                style = self.style.ERROR
                icon = "❌"
            
            self.stdout.write(
                style(
                    f"  {icon} {collection}: {status['synced_records']}/{status['total_records']} "
                    f"({sync_percentage:.1f}% synced)"
                )
            )
