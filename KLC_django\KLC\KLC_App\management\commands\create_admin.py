"""
Django management command to create admin users for the new admin panel
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import User
from django.db import transaction
from KLC_App.models import AdminProfile
import getpass


class Command(BaseCommand):
    help = 'Create a new admin user for the admin panel'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            help='Username for the admin user',
        )
        parser.add_argument(
            '--email',
            type=str,
            help='Email for the admin user',
        )
        parser.add_argument(
            '--full-permissions',
            action='store_true',
            help='Grant full permissions to the admin user',
        )
        parser.add_argument(
            '--superuser',
            action='store_true',
            help='Create a superuser with full permissions',
        )

    def handle(self, *args, **options):
        username = options.get('username')
        email = options.get('email')
        full_permissions = options.get('full_permissions', False)
        is_superuser = options.get('superuser', False)

        # Get username if not provided
        if not username:
            username = input('Username: ')

        # Check if username already exists
        if User.objects.filter(username=username).exists():
            raise CommandError(f'User with username "{username}" already exists.')

        # Get email if not provided
        if not email:
            email = input('Email (optional): ') or ''

        # Get password
        password = getpass.getpass('Password: ')
        password_confirm = getpass.getpass('Password (again): ')

        if password != password_confirm:
            raise CommandError('Passwords do not match.')

        if len(password) < 8:
            raise CommandError('Password must be at least 8 characters long.')

        # Get permissions if not superuser
        permissions = {}
        if not is_superuser and not full_permissions:
            self.stdout.write('\nSelect permissions for the admin user:')
            
            permission_choices = [
                ('can_manage_halls', 'Manage halls'),
                ('can_manage_users', 'Manage users'),
                ('can_approve_reservations', 'Approve reservations'),
                ('can_manage_transactions', 'Manage transactions'),
                ('can_view_reports', 'View reports'),
                ('can_manage_admins', 'Manage admins'),
                ('can_manage_news', 'Manage news'),
                ('can_handle_complaints', 'Handle complaints'),
            ]
            
            for perm_key, perm_desc in permission_choices:
                response = input(f'Grant "{perm_desc}" permission? (y/N): ').lower()
                permissions[perm_key] = response in ['y', 'yes']

        try:
            with transaction.atomic():
                # Create user
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    password=password,
                    is_staff=True,
                    is_superuser=is_superuser
                )

                # Create admin profile
                admin_profile = AdminProfile.objects.create(
                    user=user,
                    full_permissions=is_superuser or full_permissions,
                    **permissions
                )

                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully created admin user "{username}" with ID {user.id}'
                    )
                )
                
                if is_superuser:
                    self.stdout.write(
                        self.style.SUCCESS('User has full superuser permissions.')
                    )
                elif full_permissions:
                    self.stdout.write(
                        self.style.SUCCESS('User has full admin permissions.')
                    )
                else:
                    granted_perms = [k for k, v in permissions.items() if v]
                    if granted_perms:
                        self.stdout.write(
                            self.style.SUCCESS(f'Granted permissions: {", ".join(granted_perms)}')
                        )
                    else:
                        self.stdout.write(
                            self.style.WARNING('No specific permissions granted. User can only login.')
                        )

                self.stdout.write('\nYou can now login to the admin panel at: /admin-panel/login/')

        except Exception as e:
            raise CommandError(f'Error creating admin user: {str(e)}')


class CreateSuperAdminCommand(BaseCommand):
    """Quick command to create a superuser admin"""
    help = 'Create a superuser admin with full permissions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            default='admin',
            help='Username for the superuser (default: admin)',
        )
        parser.add_argument(
            '--password',
            type=str,
            help='Password for the superuser',
        )

    def handle(self, *args, **options):
        username = options.get('username', 'admin')
        password = options.get('password')

        # Check if username already exists
        if User.objects.filter(username=username).exists():
            raise CommandError(f'User with username "{username}" already exists.')

        # Get password if not provided
        if not password:
            password = getpass.getpass('Password: ')

        if len(password) < 8:
            raise CommandError('Password must be at least 8 characters long.')

        try:
            with transaction.atomic():
                # Create superuser
                user = User.objects.create_superuser(
                    username=username,
                    email='<EMAIL>',
                    password=password
                )

                # Create admin profile with full permissions
                admin_profile = AdminProfile.objects.create(
                    user=user,
                    full_permissions=True
                )

                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully created superuser admin "{username}" with full permissions'
                    )
                )
                self.stdout.write('\nYou can now login to the admin panel at: /admin-panel/login/')

        except Exception as e:
            raise CommandError(f'Error creating superuser admin: {str(e)}')
