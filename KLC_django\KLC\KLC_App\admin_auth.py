"""
Admin Authentication System
Provides decorators and utilities for admin authentication and authorization
"""

from functools import wraps
from django.shortcuts import redirect, render
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.contrib import messages
from django.http import JsonResponse, HttpResponseForbidden
from django.utils import timezone
from django.conf import settings
from .models import AdminProfile
import logging

logger = logging.getLogger(__name__)


def admin_required(permission=None):
    """
    Decorator to require admin authentication and optionally specific permissions
    
    Args:
        permission (str): Optional specific permission required
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Check if user is authenticated
            if not request.user.is_authenticated:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({'error': 'Authentication required'}, status=401)
                return redirect('admin_login')
            
            # Check if user has admin profile
            try:
                admin_profile = AdminProfile.objects.get(user=request.user)
            except AdminProfile.DoesNotExist:
                messages.error(request, 'ليس لديك صلاحيات إدارية.')
                logout(request)
                return redirect('admin_login')
            
            # Check if account is locked
            if admin_profile.is_account_locked():
                messages.error(request, 'حسابك مقفل مؤقتاً. يرجى المحاولة لاحقاً.')
                logout(request)
                return redirect('admin_login')
            
            # Check specific permission if required
            if permission and not admin_profile.has_permission(permission):
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({'error': 'Permission denied'}, status=403)
                return render(request, 'KLC_App/admin/permission_denied.html', {
                    'required_permission': permission,
                    'admin_profile': admin_profile
                })
            
            # Add admin profile to request for easy access
            request.admin_profile = admin_profile
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def admin_login_required(view_func):
    """Simple decorator that only requires admin login without specific permissions"""
    return admin_required()(view_func)


class AdminAuthenticationService:
    """Service class for handling admin authentication operations"""
    
    @staticmethod
    def authenticate_admin(username, password, request=None):
        """
        Authenticate admin user with enhanced security
        
        Args:
            username (str): Username
            password (str): Password
            request: HTTP request object for IP tracking
            
        Returns:
            tuple: (success: bool, user: User|None, message: str)
        """
        try:
            # Get user
            user = User.objects.get(username=username, is_active=True)
            admin_profile = AdminProfile.objects.get(user=user)
            
            # Check if account is locked
            if admin_profile.is_account_locked():
                return False, None, f'الحساب مقفل حتى {admin_profile.account_locked_until.strftime("%H:%M")}'
            
            # Authenticate user
            authenticated_user = authenticate(username=username, password=password)
            
            if authenticated_user:
                # Reset failed login attempts
                admin_profile.reset_failed_login()
                
                # Update last login IP if request provided
                if request:
                    admin_profile.last_login_ip = AdminAuthenticationService.get_client_ip(request)
                    admin_profile.save()
                
                logger.info(f'Admin login successful: {username}')
                return True, authenticated_user, 'تم تسجيل الدخول بنجاح'
            else:
                # Increment failed login attempts
                admin_profile.increment_failed_login()
                logger.warning(f'Admin login failed: {username}')
                return False, None, 'اسم المستخدم أو كلمة المرور غير صحيحة'
                
        except User.DoesNotExist:
            logger.warning(f'Admin login attempt with non-existent user: {username}')
            return False, None, 'اسم المستخدم أو كلمة المرور غير صحيحة'
        except AdminProfile.DoesNotExist:
            logger.warning(f'Login attempt by non-admin user: {username}')
            return False, None, 'ليس لديك صلاحيات إدارية'
        except Exception as e:
            logger.error(f'Admin authentication error: {str(e)}')
            return False, None, 'حدث خطأ في النظام'
    
    @staticmethod
    def get_client_ip(request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    @staticmethod
    def create_admin_user(username, password, email, permissions=None, created_by=None):
        """
        Create a new admin user with specified permissions
        
        Args:
            username (str): Username
            password (str): Password
            email (str): Email address
            permissions (dict): Dictionary of permissions
            created_by (User): User who created this admin
            
        Returns:
            tuple: (success: bool, admin_profile: AdminProfile|None, message: str)
        """
        try:
            # Check if username already exists
            if User.objects.filter(username=username).exists():
                return False, None, 'اسم المستخدم موجود بالفعل'
            
            # Create user
            user = User.objects.create_user(
                username=username,
                password=password,
                email=email,
                is_staff=True  # Mark as staff for Django admin access if needed
            )
            
            # Create admin profile
            admin_profile = AdminProfile.objects.create(
                user=user,
                created_by=created_by,
                **permissions if permissions else {}
            )
            
            logger.info(f'Admin user created: {username} by {created_by.username if created_by else "system"}')
            return True, admin_profile, 'تم إنشاء المشرف بنجاح'
            
        except Exception as e:
            logger.error(f'Error creating admin user: {str(e)}')
            return False, None, f'حدث خطأ: {str(e)}'
    
    @staticmethod
    def update_admin_permissions(admin_profile, permissions):
        """
        Update admin permissions
        
        Args:
            admin_profile (AdminProfile): Admin profile to update
            permissions (dict): New permissions
            
        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            for permission, value in permissions.items():
                if hasattr(admin_profile, permission):
                    setattr(admin_profile, permission, value)
            
            admin_profile.save()
            logger.info(f'Admin permissions updated: {admin_profile.user.username}')
            return True, 'تم تحديث الصلاحيات بنجاح'
            
        except Exception as e:
            logger.error(f'Error updating admin permissions: {str(e)}')
            return False, f'حدث خطأ: {str(e)}'
    
    @staticmethod
    def delete_admin_user(admin_profile, deleted_by=None):
        """
        Delete admin user (soft delete by deactivating)
        
        Args:
            admin_profile (AdminProfile): Admin profile to delete
            deleted_by (User): User who performed the deletion
            
        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            # Deactivate user instead of hard delete
            admin_profile.user.is_active = False
            admin_profile.user.save()
            
            logger.info(f'Admin user deactivated: {admin_profile.user.username} by {deleted_by.username if deleted_by else "system"}')
            return True, 'تم حذف المشرف بنجاح'
            
        except Exception as e:
            logger.error(f'Error deleting admin user: {str(e)}')
            return False, f'حدث خطأ: {str(e)}'


def get_admin_context(request):
    """
    Get common admin context data
    
    Args:
        request: HTTP request object
        
    Returns:
        dict: Context data for admin templates
    """
    context = {}
    
    if hasattr(request, 'admin_profile'):
        context.update({
            'admin_profile': request.admin_profile,
            'admin_username': request.user.username,
            'admin_full_name': request.user.get_full_name() or request.user.username,
            'admin_permissions': {
                'manage_halls': request.admin_profile.has_permission('manage_halls'),
                'manage_users': request.admin_profile.has_permission('manage_users'),
                'approve_reservations': request.admin_profile.has_permission('approve_reservations'),
                'manage_transactions': request.admin_profile.has_permission('manage_transactions'),
                'view_reports': request.admin_profile.has_permission('view_reports'),
                'manage_admins': request.admin_profile.has_permission('manage_admins'),
                'manage_news': request.admin_profile.has_permission('manage_news'),
                'handle_complaints': request.admin_profile.has_permission('handle_complaints'),
            }
        })
    
    return context
