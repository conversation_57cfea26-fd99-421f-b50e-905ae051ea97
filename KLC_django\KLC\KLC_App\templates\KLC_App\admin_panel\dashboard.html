{% extends 'KLC_App/admin_panel/base.html' %}

{% block title %}الرئيسية{% endblock %}
{% block page_title %}لوحة التحكم الرئيسية{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="mb-8">
    <div class="bg-gradient-to-l from-primary-500 to-secondary-500 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">مرحباً، {{ admin_full_name }}</h1>
                <p class="text-primary-100">إليك نظرة عامة على نشاط النظام اليوم</p>
            </div>
            <div class="hidden md:block">
                <i class="fas fa-chart-line text-4xl text-primary-200"></i>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Users -->
    <div class="bg-white rounded-xl p-6 card-shadow hover-scale">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">إجمالي المستخدمين</p>
                <p class="text-3xl font-bold text-gray-900">{{ stats.total_users }}</p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-users text-blue-600 text-xl"></i>
            </div>
        </div>
    </div>
    
    <!-- Total Reservations -->
    <div class="bg-white rounded-xl p-6 card-shadow hover-scale">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">إجمالي الحجوزات</p>
                <p class="text-3xl font-bold text-gray-900">{{ stats.total_reservations }}</p>
                <p class="text-green-600 text-sm">{{ stats.confirmed_reservations }} مؤكد</p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-calendar-check text-green-600 text-xl"></i>
            </div>
        </div>
    </div>
    
    <!-- Pending Reservations -->
    <div class="bg-white rounded-xl p-6 card-shadow hover-scale">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">حجوزات في الانتظار</p>
                <p class="text-3xl font-bold text-gray-900">{{ stats.pending_reservations }}</p>
            </div>
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-clock text-yellow-600 text-xl"></i>
            </div>
        </div>
    </div>
    
    <!-- Total Transactions -->
    <div class="bg-white rounded-xl p-6 card-shadow hover-scale">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">إجمالي المعاملات</p>
                <p class="text-3xl font-bold text-gray-900">{{ stats.total_transactions }}</p>
                <p class="text-blue-600 text-sm">{{ stats.completed_transactions }} مكتمل</p>
            </div>
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-exchange-alt text-purple-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Reservations Chart -->
    <div class="bg-white rounded-xl p-6 card-shadow">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">توزيع الحجوزات</h3>
        <div class="relative h-64">
            <canvas id="reservationsChart"></canvas>
        </div>
    </div>
    
    <!-- Transactions Chart -->
    <div class="bg-white rounded-xl p-6 card-shadow">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">توزيع المعاملات</h3>
        <div class="relative h-64">
            <canvas id="transactionsChart"></canvas>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Recent Reservations -->
    <div class="bg-white rounded-xl p-6 card-shadow">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">أحدث الحجوزات</h3>
            {% if admin_permissions.approve_reservations %}
            <a href="{% url 'admin_reservation_management' %}" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                عرض الكل
            </a>
            {% endif %}
        </div>
        <div class="space-y-3">
            {% for reservation in recent_reservations %}
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-900">{{ reservation.full_name }}</p>
                    <p class="text-sm text-gray-600">{{ reservation.event_type }}</p>
                </div>
                <span class="px-2 py-1 text-xs rounded-full 
                    {% if reservation.reservation_status == 'confirmed' %}bg-green-100 text-green-800
                    {% elif reservation.reservation_status == 'in_progress' %}bg-yellow-100 text-yellow-800
                    {% elif reservation.reservation_status == 'cancelled' %}bg-red-100 text-red-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                    {{ reservation.get_reservation_status_display }}
                </span>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">لا توجد حجوزات حديثة</p>
            {% endfor %}
        </div>
    </div>
    
    <!-- Recent Transactions -->
    <div class="bg-white rounded-xl p-6 card-shadow">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">أحدث المعاملات</h3>
            {% if admin_permissions.manage_transactions %}
            <a href="{% url 'admin_transaction_management' %}" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                عرض الكل
            </a>
            {% endif %}
        </div>
        <div class="space-y-3">
            {% for transaction in recent_transactions %}
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-900">{{ transaction.full_name }}</p>
                    <p class="text-sm text-gray-600">{{ transaction.transaction_type }}</p>
                </div>
                <span class="px-2 py-1 text-xs rounded-full 
                    {% if transaction.transaction_status == 'done' %}bg-green-100 text-green-800
                    {% elif transaction.transaction_status == 'in_progress' %}bg-yellow-100 text-yellow-800
                    {% else %}bg-red-100 text-red-800{% endif %}">
                    {{ transaction.get_transaction_status_display }}
                </span>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">لا توجد معاملات حديثة</p>
            {% endfor %}
        </div>
    </div>
    
    <!-- Recent Complaints -->
    <div class="bg-white rounded-xl p-6 card-shadow">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">أحدث الشكاوي</h3>
            {% if admin_permissions.handle_complaints %}
            <a href="{% url 'admin_complaint_management' %}" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                عرض الكل
            </a>
            {% endif %}
        </div>
        <div class="space-y-3">
            {% for complaint in recent_complaints %}
            <div class="p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                    <p class="font-medium text-gray-900">{{ complaint.full_name }}</p>
                    {% if not complaint.is_read %}
                    <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                    {% endif %}
                </div>
                <p class="text-sm text-gray-600">{{ complaint.suggestion_complaint_type }}</p>
                <p class="text-xs text-gray-500 mt-1">{{ complaint.created_at|date:"d/m/Y" }}</p>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">لا توجد شكاوي حديثة</p>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="mt-8">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h3>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        {% if admin_permissions.approve_reservations %}
        <a href="{% url 'admin_reservation_management' %}" class="bg-white p-4 rounded-lg card-shadow hover-scale text-center">
            <i class="fas fa-calendar-plus text-2xl text-primary-600 mb-2"></i>
            <p class="text-sm font-medium text-gray-900">إدارة الحجوزات</p>
        </a>
        {% endif %}
        
        {% if admin_permissions.manage_users %}
        <a href="{% url 'admin_user_management' %}" class="bg-white p-4 rounded-lg card-shadow hover-scale text-center">
            <i class="fas fa-user-plus text-2xl text-blue-600 mb-2"></i>
            <p class="text-sm font-medium text-gray-900">إدارة المستخدمين</p>
        </a>
        {% endif %}
        
        {% if admin_permissions.manage_transactions %}
        <a href="{% url 'admin_transaction_management' %}" class="bg-white p-4 rounded-lg card-shadow hover-scale text-center">
            <i class="fas fa-file-invoice text-2xl text-green-600 mb-2"></i>
            <p class="text-sm font-medium text-gray-900">إدارة المعاملات</p>
        </a>
        {% endif %}
        
        {% if admin_permissions.view_reports %}
        <a href="{% url 'admin_reports' %}" class="bg-white p-4 rounded-lg card-shadow hover-scale text-center">
            <i class="fas fa-chart-bar text-2xl text-purple-600 mb-2"></i>
            <p class="text-sm font-medium text-gray-900">التقارير</p>
        </a>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Reservations Chart
    const reservationsCtx = document.getElementById('reservationsChart').getContext('2d');
    const reservationsData = {{ reservation_chart_data|safe }};
    
    new Chart(reservationsCtx, {
        type: 'doughnut',
        data: {
            labels: reservationsData.labels,
            datasets: [{
                data: reservationsData.data,
                backgroundColor: [
                    '#FCD34D', // Yellow for in_progress
                    '#10B981', // Green for confirmed
                    '#EF4444', // Red for cancelled
                    '#6B7280'  // Gray for expired
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
    
    // Transactions Chart
    const transactionsCtx = document.getElementById('transactionsChart').getContext('2d');
    const transactionsData = {{ transaction_chart_data|safe }};
    
    new Chart(transactionsCtx, {
        type: 'bar',
        data: {
            labels: transactionsData.labels,
            datasets: [{
                data: transactionsData.data,
                backgroundColor: [
                    '#FCD34D', // Yellow for in_progress
                    '#10B981', // Green for done
                    '#EF4444'  // Red for cancelled
                ],
                borderRadius: 8,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
</script>
{% endblock %}
