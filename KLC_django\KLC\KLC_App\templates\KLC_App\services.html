{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>مجلس قروي كفرعين - خدماتي</title>
    <!-- Force light mode by default -->
    <script>
      // Set light mode immediately before any other scripts run
      document.documentElement.setAttribute("data-theme", "light");
      localStorage.setItem("theme", "light");
    </script>
    <!-- Bootstrap 5.3 CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link href="{% static 'css/index.css' %}" rel="stylesheet" />
    <!-- Theme CSS -->
    <link href="{% static 'css/theme.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <link
      rel="icon"
      type="image/x-icon"
      href="{% static 'images/logo.png' %}"
    />
    <!--Link the js file-->
    <script src="{% static 'js/services.js' %}" defer></script>
    <!-- Theme JS -->
    <script src="{% static 'js/theme.js' %}?v={{ STATIC_VERSION }}" defer></script>
    <!--Link the footer js file-->
    <script src="{% static 'js/footer.js' %}" defer></script>

    <!-- Font Awesome CDN -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />

    <!-- Custom styles for the page -->
    <style>
      /* Fixed header padding */
      body {
        padding-top: 6rem;
      }

      /* Services section spacing */
      #services-section {
        margin-top: 3.5rem !important;
        padding-top: 2rem !important;
      }

      /* Water service button styles */
      #waterServiceBtn {
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
        z-index: 1;
      }

      #waterServiceBtn::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(52, 152, 219, 0.2) 0%,
          rgba(52, 152, 219, 0) 100%
        );
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      #waterServiceBtn:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
      }

      #waterServiceBtn:hover::before {
        opacity: 1;
      }

      #waterServiceBtn img {
        transition: transform 0.3s ease;
      }

      #waterServiceBtn:hover img {
        transform: scale(1.05);
      }

      #waterServiceBtn .badge {
        box-shadow: 0 3px 10px rgba(52, 152, 219, 0.3);
        transition: all 0.3s ease;
      }

      #waterServiceBtn:hover .badge {
        transform: scale(1.1) translate(-40%, -40%);
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.5);
      }
    </style>
  </head>
  <body id="page-top">
    <!-- Professional Navigation Bar -->
    <nav
      class="navbar navbar-expand-lg navbar-dark fixed-top shadow-lg"
      id="mainNav"
    >
      <div class="container px-4">
        <!-- Logo and title -->
        <a
          class="logo d-flex align-items-center text-decoration-none"
          href="/services"
          title="صفحة خدماتي"
        >
          <img
            src="{% static 'images/logo.png' %}"
            alt="شعار الموقع"
            width="150"
            height="100"
            class="d-inline-block"
          />
          <span class="ms-2 d-none d-lg-inline text-light fw-bold text-shadow">خدماتي</span>
        </a>
        <button
          class="navbar-toggler border-0 shadow-sm"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarResponsive"
          aria-controls="navbarResponsive"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarResponsive">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link px-3 mx-1 rounded-pill" href="#complaints-suggestions">
                <i class="fas fa-comment-alt me-1"></i> شكاوى واقتراحات
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link px-3 mx-1 rounded-pill" href="#contact-us">
                <i class="fas fa-envelope me-1"></i> تواصل معنا
              </a>
            </li>
            <li class="nav-item">
              <a
                class="nav-link px-3 mx-1 rounded-pill"
                href="{% url 'index' %}"
              >
                <i class="fas fa-sign-out-alt me-1"></i>
                <span>خروج</span>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </nav>
    <!-- End of Navbar -->
    <!-- check the suggestions and complaints form status-->
    {% if suggestions_status and request.method == "POST" %}
    <div class="alert text-center mt-1 mb-0" role="alert" id="alertMsg">
      {% if suggestions_status == "success" %}
      <script>
        const delay = 5000; // 5 seconds delay
        //create an alert message within a div
        const alertDiv = document.createElement("div");
        alertDiv.className = "alert alert-success text-center";
        alertDiv.role = "alert";
        alertDiv.innerHTML = `
              <strong>نجاح:</strong> تم إرسال الطلب بنجاح!
            `;
        document.getElementById("alertMsg").appendChild(alertDiv);
        // Hide the alert after 5 seconds
        setTimeout(() => {
          alertDiv.style.display = "none";
        }, delay);
      </script>
      {% elif suggestions_status == "conflict suggestions complaints" %}
      <script>
        const delay = 8000; // 8 seconds delay
        //create an alert message within a div
        const alertDiv = document.createElement("div");
        alertDiv.className = "alert alert-warning text-center";
        alertDiv.role = "alert";
        alertDiv.innerHTML = `
              <strong>تحذير:</strong> لم يتم إرسال الطلب، لقد تجاوزت الحد الأقصى من الشكاوى أو الإقتراحات، للمزيد يمكنك مراجعة المجلس.
            `;
        document.getElementById("alertMsg").appendChild(alertDiv);
        // Hide the alert after 8 seconds
        setTimeout(() => {
          alertDiv.style.display = "none";
        }, delay);
      </script>
      {% else %}
      <script>
        const delay = 5000; // 5 seconds delay
        //create an alert message within a div
        const alertDiv = document.createElement("div");
        alertDiv.className = "alert alert-danger text-center";
        alertDiv.role = "alert";
        alertDiv.innerHTML = `
            <strong>خطأ:</strong> حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.
          `;
        document.getElementById("alertMsg").appendChild(alertDiv);
        // Hide the alert after 5 seconds
        setTimeout(() => {
          alertDiv.style.display = "none";
        }, delay);
      </script>
      {% endif %}
    </div>
    {% endif %}
    <!-- Services Section -->
    <section
      id="services-section"
      class="container bg-dark bg-opacity-50 p-4 rounded shadow"
    >
      <div class="row justify-content-center gap-4">
        <!-- Services title -->
        <div class="col-12 text-center mb-3">
          <h2 class="text-light border-bottom pb-3 border-danger">
            تمتع بخدمات المجلس الإلكترونية
          </h2>
          <p class="lead text-light shadow fs-4">إختر الخدمة التي ترغب بها</p>
          {% if status and request.method == "POST" %}
          <div class="alert text-center mt-1 mb-0" role="alert" id="alertMsg">
            {% if status == "success" %}
            <script>
              // Enable smooth scrolling for anchor links
              document
                .querySelectorAll('a.nav-link[href^="#"]')
                .forEach((anchor) => {
                  anchor.addEventListener("click", function (e) {
                    e.preventDefault();
                    document
                      .querySelector(this.getAttribute("href"))
                      .scrollIntoView({
                        behavior: "smooth",
                      });
                  });
                });
              const delay = 5000; // 5 seconds delay
              //create an alert message within a div
              const alertDiv = document.createElement("div");
              alertDiv.className = "alert alert-success text-center";
              alertDiv.role = "alert";
              alertDiv.innerHTML = `
                    <strong>نجاح:</strong> تم إرسال الطلب بنجاح!
                  `;
              document.getElementById("alertMsg").appendChild(alertDiv);
              // Hide the alert after 5 seconds
              setTimeout(() => {
                alertDiv.style.display = "none";
              }, delay);
            </script>
            <!-- Please note that: the conflict slot status only for hall reservation -->
            {% elif status == "conflict slot" %}
            <script>
              const delay = 5000; // 5 seconds delay
              //create an alert message within a div
              const alertDiv = document.createElement("div");
              alertDiv.className = "alert alert-warning text-center";
              alertDiv.role = "alert";
              alertDiv.innerHTML = `
                    <strong>تحذير:</strong> لم يتم إرسال الطلب، لديك حجز بالفعل.
                  `;
              document.getElementById("alertMsg").appendChild(alertDiv);
              // Hide the alert after 5 seconds
              setTimeout(() => {
                alertDiv.style.display = "none";
              }, delay);
            </script>
            {% elif status == "policy_violation" %}
            <script>
              const delay = 7000; // 7 seconds delay for policy messages
              //create an alert message within a div
              const alertDiv = document.createElement("div");
              alertDiv.className = "alert alert-danger text-center";
              alertDiv.role = "alert";
              alertDiv.innerHTML = `
                    <strong>خطأ في السياسة:</strong> {{ error_message|default:"لا يمكن إنشاء الحجز وفقاً لسياسة النظام الجديدة." }}
                  `;
              document.getElementById("alertMsg").appendChild(alertDiv);
              // Hide the alert after 7 seconds
              setTimeout(() => {
                alertDiv.style.display = "none";
              }, delay);
            </script>
            {% elif status == "conflict transaction" %}
            <script>
              const delay = 5000; // 5 seconds delay
              //create an alert message within a div
              const alertDiv = document.createElement("div");
              alertDiv.className = "alert alert-warning text-center";
              alertDiv.role = "alert";
              alertDiv.innerHTML = `
                    <strong>تحذير:</strong> لم يتم إرسال الطلب، لديك طلب بالفعل بنفس نوع المعاملة.
                  `;
              document.getElementById("alertMsg").appendChild(alertDiv);
              // Hide the alert after 5 seconds
              setTimeout(() => {
                alertDiv.style.display = "none";
              }, delay);
            </script>
            <!-- Please note that: this date is already reserved status only for hall reservation -->
            {% elif status == "this date is already reserved" %}
            <script>
              const delay = 6000; // 6 seconds delay
              //create an alert message within a div
              const alertDiv = document.createElement("div");
              alertDiv.className = "alert alert-warning text-center";
              alertDiv.role = "alert";
              alertDiv.innerHTML = `
                    <strong>تحذير:</strong> لم يتم إرسال الطلب، يوجد تعارض في تواريخ الحجز أو حجز بالفعل في نفس الفترة التي قمت بإختيارها.
                  `;
              document.getElementById("alertMsg").appendChild(alertDiv);
              // Hide the alert after 6 seconds
              setTimeout(() => {
                alertDiv.style.display = "none";
              }, delay);
            </script>
            {% else %}
            <script>
              const delay = 5000; // 5 seconds delay
              //create an alert message within a div
              const alertDiv = document.createElement("div");
              alertDiv.className = "alert alert-danger text-center";
              alertDiv.role = "alert";
              alertDiv.innerHTML = `
                    <strong>خطأ:</strong> حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.
                  `;
              document.getElementById("alertMsg").appendChild(alertDiv);
              // Hide the alert after 5 seconds
              setTimeout(() => {
                alertDiv.style.display = "none";
              }, delay);
            </script>
            {% endif %}
          </div>
          {% endif %}
        </div>
        <!-- Service buttons -->
        <div class="row text-center g-3 justify-content-center w-100 mt-0">
          <div class="col-md-3">
            {% if user_debts_amount <= 240 %}
            <button
              id="transactionBtn"
              type="button"
              class="btn btn-rounded w-100 text-white fs-4"
              data-bs-toggle="modal"
              data-bs-target="#transactionsModal"
              title="إضغط لطلب معاملة"
            >
              <div class="d-flex flex-column align-items-center">
                <img
                  src="{% static 'images/transaction_logo.png' %}"
                  class="bi me-2 text-white"
                  alt="طلب معاملة"
                  width="100"
                  height="100"
                  class="d-inline-block align-text-top"
                />
                <span>طلب معاملة</span>
              </div>
            </button>
            {% else %}
            <button
              id="transactionBtn"
              type="button"
              class="btn btn-rounded w-100 text-white fs-4"
              data-bs-toggle="modal"
              data-bs-target="#debtRestrictionModal"
              title="إضغط لطلب معاملة"
            >
              <div class="d-flex flex-column align-items-center">
                <img
                  src="{% static 'images/transaction_logo.png' %}"
                  class="bi me-2 text-white"
                  alt="طلب معاملة"
                  width="100"
                  height="100"
                  class="d-inline-block align-text-top"
                />
                <span>طلب معاملة</span>
              </div>
            </button>
            {% endif %}
          </div>
          <div class="col-md-3">
            <a
              id="tabooBtn"
              class="btn btn-rounded w-100 text-white fs-4 text-decoration-none"
              title="إضغط لعرض أحواض قرية كفرعين"
            >
              <div class="d-flex flex-column align-items-center">
                <img
                  src="{% static 'images/taboo_logo.png' %}"
                  class="bi me-2 text-white"
                  alt="أحواض قرية كفرعين"
                  width="100"
                  height="100"
                  class="d-inline-block align-text-top"
                />
                <span>أحواض قرية كفرعين</span>
              </div>
            </a>
          </div>
          <div class="col-md-3">
            <button
              id="hallReservationBtn"
              type="button"
              class="btn btn-rounded w-100 text-white fs-4"
              data-bs-toggle="modal"
              data-bs-target="#hallReservationModal"
              title="إضغط لحجز قاعة المجلس"
            >
              <div class="d-flex flex-column align-items-center">
                <img
                  src="{% static 'images/hall_logo.png' %}"
                  class="bi me-2 text-white"
                  alt="إحجز قاعة المجلس"
                  width="100"
                  height="100"
                  class="d-inline-block align-text-top"
                />
                <span>إحجز قاعة المجلس</span>
              </div>
            </button>
          </div>
          <div class="col-md-3">
            {% if user_debts_amount > 240 %}
            <button
              id="debtsCheckBtn"
              type="button"
              class="btn btn-rounded w-100 text-white fs-4"
              data-bs-toggle="modal"
              data-bs-target="#debtsCheckModal"
              title="إضغط للتحقق من مستحقات النفايات"
            >
              <div class="d-flex flex-column align-items-center">
                <img
                  src="{% static 'images/check_debts_logo.png' %}"
                  class="bi me-2 text-white"
                  alt="تحقق من مستحقات النفايات"
                  width="100"
                  height="100"
                  class="d-inline-block align-text-top"
                />
                <span>تحقق من مستحقات النفايات</span>
              </div>
            </button>
            {% else %}
            <button
              id="debtsCheckBtn"
              type="button"
              class="btn btn-rounded w-100 text-white fs-4"
              data-bs-toggle="modal"
              data-bs-target="#noDebtsModal"
              title="إضغط للتحقق من مستحقات النفايات"
            >
              <div class="d-flex flex-column align-items-center">
                <img
                  src="{% static 'images/check_debts_logo.png' %}"
                  class="bi me-2 text-white"
                  alt="تحقق من مستحقات النفايات"
                  width="100"
                  height="100"
                  class="d-inline-block align-text-top"
                />
                <span>تحقق من مستحقات النفايات</span>
              </div>
            </button>
            {% endif %}
          </div>
          <div class="col-md-3">
            <button
              id="waterServiceBtn"
              type="button"
              class="btn btn-rounded w-100 text-white fs-4 position-relative"
              data-bs-toggle="modal"
              data-bs-target="#waterServiceModal"
              title="إضغط للمزيد عن خدمة شحن عدادات المياه"
            >
              <div class="d-flex flex-column align-items-center">
                <div class="position-relative">
                  <img
                    src="{% static 'images/water_logo.png' %}"
                    alt="شحن عدادات المياه"
                    width="120"
                    height="110"
                    class="d-inline-block align-text-top"
                  />
                  <span
                    class="position-absolute top-0 start-0 translate-middle badge rounded-pill bg-primary"
                    style="font-size: 0.7rem"
                  >
                    خدمة جديدة
                  </span>
                </div>
                <span class="mt-2">شحن عدادات المياه</span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </section>
    <!-- End of Services Section -->
    <!-- Complaints and Suggestions Section -->
    <section id="complaints-suggestions">
      <div class="container px-4 my-5">
        <div class="row gx-4 justify-content-center">
          <div
            class="col-lg-8 text-light text-center mb-4 bg-dark bg-gradient bg-opacity-75 p-3 rounded shadow border border-danger border-2"
          >
            <h2>صندوق الشكاوى والإقتراحات</h2>
            <p class="lead mb-4 border-bottom border-danger pb-3">
              نرحب بآرائكم ومقترحاتكم، فهي تعكس مدى رضاكم عن خدماتنا. نسعى
              دائماً لتحسين خدماتنا، شاركنا أفكارك واقتراحاتك لتكون جزءاً من
              التطوير والتميز.
            </p>
            <form
              method="post"
              action="{% url 'make_suggestions_complaints' %}"
            >
              {% csrf_token %}
              <div class="mb-3">
                <label for="suggestionsProviderName" class="form-label"
                  >الإسم الرباعي (إختياري)</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="suggestionsProviderName"
                  name="suggestionsProviderName"
                  maxlength="40"
                  title="الإسم الرباعي"
                  oninput="this.value = this.value.replace(/[^ء-ي ]/g, '')"
                  placeholder="أدخل الإسم الرباعي باللغة العربية (إختياري)"
                />
              </div>
              <div class="mb-3">
                <!-- The allowed letters only the arabic letters and with some punctuation marks (: . ،) -->
                <label for="suggestionsMessage" class="form-label"
                  >نص الشكوى أو الإقتراح</label
                >
                <textarea
                  class="form-control"
                  id="suggestionsMessage"
                  name="suggestionsMessage"
                  rows="5"
                  placeholder="أدخل نص الشكوى أو الإقتراح هنا..."
                  maxlength="120"
                  oninput="this.value = this.value.replace(/[^ء-ي .,،:]/g, '')"
                  title="نص الشكوى أو الإقتراح يجب أن يتكون من حروف عربية فقط"
                  required
                ></textarea>
                <div class="text-end text-light">
                  <small>عدد الحروف:</small>
                  <span id="charCount">0</span>/120
                </div>
              </div>
              <button type="submit" class="btn btn-danger">إرسال</button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- Modals -->
    <!-- Get transaction modal template -->
    {% include 'KLC_App/transaction_modal.html' %}
    <!-- Get Hall Reservation modal template -->
    {% include 'KLC_App/hall_reservation_modal.html' %}
    <!-- Get debt restriction modal template - For the users have debts more than 0-->
    {% include 'KLC_App/debt_restriction_modal.html' %}
    <!-- Get debts check modal template - For the users have debts more than 0-->
    {% include 'KLC_App/exist_debts_modal.html' %}
    <!-- Get no debts modal template - For the users have debts equal to 0-->
    {% include 'KLC_App/no_debts_modal.html' %}
    <!--Get water service modal -->
    {% include 'KLC_App/water_service_modal.html' %}
    <!-- Footer-->
    <div id="contact-us">{% include 'KLC_App/footer.html' %}</div>

    <!-- Theme Toggle Button -->
    <button class="theme-toggle" aria-label="Toggle theme">
      <i class="fas fa-moon"></i>
    </button>

    <!-- Bootstrap 5.3 JS and Popper.js (needed for some components) -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.min.js"></script>
    <script>
      /// function to redirect to taboo page
      function redirectToTaboo() {
        tabooBtn.addEventListener("click", function (event) {
          event.preventDefault(); // Prevent the default action
          const delay = 4000; // 4 seconds delay
          const loadingAnimation = document.createElement("div");
          loadingAnimation.className = "loading-animation";
          loadingAnimation.innerHTML = `
                    <div class="loader"></div>
                    <p class="text-center" style="color: white; font-size: 1.5rem; margin-top: 20px;">سيتم تحويلك إلى صفحة مكتب تسوية كفرعين/هيئة تسوية الأراضي والمياه، إنتظر قليلاً.....</p>
                `;
          loadingAnimation.style.position = "fixed";
          loadingAnimation.style.top = "50%";
          loadingAnimation.style.left = "50%";
          loadingAnimation.style.transform = "translate(-50%, -50%)";
          loadingAnimation.style.zIndex = "9999";
          loadingAnimation.style.display = "flex";
          loadingAnimation.style.flexDirection = "column";
          loadingAnimation.style.justifyContent = "center";
          loadingAnimation.style.alignItems = "center";
          loadingAnimation.style.background = "rgba(0, 0, 0, 0.8)";
          loadingAnimation.style.width = "100vw";
          loadingAnimation.style.height = "100vh";

          const loader = loadingAnimation.querySelector(".loader");
          loader.style.border = "16px solid #f3f3f3";
          loader.style.borderTop = "16px solid rgb(197, 79, 0)";
          loader.style.borderRadius = "50%";
          loader.style.width = "120px";
          loader.style.height = "120px";
          loader.style.animation = "spin 2s linear infinite";

          const style = document.createElement("style");
          style.innerHTML = `
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                `;
          document.head.appendChild(style);

          document.body.appendChild(loadingAnimation);
          setTimeout(() => {
            document.body.removeChild(loadingAnimation);
            window.open(
              "https://lwsc.ps/mob/works3.php?id=142",
              "_blank",
              "noopener,noreferrer"
            );
          }, delay);
        });
      }
      // function to add hover effect to the services buttons
      function addHoverEffect(button) {
        button.addEventListener("mouseover", function () {
          button.classList.add("btn-hover");
        });
        button.addEventListener("mouseout", function () {
          button.classList.remove("btn-hover");
        });
      }
      document.addEventListener("DOMContentLoaded", function () {
        redirectToTaboo();
        // Add hover effect to the services buttons
        const transactionBtn = document.getElementById("transactionBtn");
        const tabooBtn = document.getElementById("tabooBtn");
        const hallReservationBtn =
          document.getElementById("hallReservationBtn");
        const debtsCheckBtn = document.getElementById("debtsCheckBtn");
        const waterServiceBtn = document.getElementById("waterServiceBtn");
        addHoverEffect(transactionBtn);
        addHoverEffect(tabooBtn);
        addHoverEffect(hallReservationBtn);
        addHoverEffect(debtsCheckBtn);
        addHoverEffect(waterServiceBtn);
      });
      // create the hover style.innerHTML for the button
      const style = document.createElement("style");
      style.innerHTML = `
          .btn-hover {
            transform: translateY(-20px);
            transition: transform 0.4s ease;
        `;
      document.head.appendChild(style);
    </script>
    <!-- Script to handle the character count for the suggestions message -->
    <script>
      const suggestionsMessage = document.getElementById("suggestionsMessage");
      const charCount = document.getElementById("charCount");

      suggestionsMessage.addEventListener("input", function () {
        charCount.textContent = this.value.length;
      });
    </script>
  </body>
</html>
