"""
Reservation Policy Validation Service

This module contains the business logic for validating hall reservations
based on user types and existing reservation status.
"""

from django.contrib.auth.models import User
from .models import Person, HallReservation


class ReservationPolicyValidator:
    """
    Service class for validating reservation policies
    """
    
    # Error messages
    USER_PENDING_RESERVATION_ERROR = "You already have a pending reservation. Please confirm or cancel it before reserving again."
    ADMIN_NO_CONFIRMED_RESERVATION_ERROR = "This user must have at least one confirmed reservation before an admin can reserve on their behalf."
    
    @classmethod
    def can_user_reserve(cls, person):
        """
        Check if a regular user can make a new reservation
        
        Policy: A user can make a new reservation only if:
        - They do not already have a reservation in pending status
        - Or they only have confirmed reservations
        
        Args:
            person: Person object
            
        Returns:
            tuple: (can_reserve: bool, error_message: str)
        """
        # Check for pending reservations (in_progress status)
        pending_reservations = HallReservation.objects.filter(
            person=person,
            reservation_status='in_progress'
        )
        
        if pending_reservations.exists():
            return False, cls.USER_PENDING_RESERVATION_ERROR
        
        return True, ""
    
    @classmethod
    def can_admin_reserve_for(cls, person):
        """
        Check if an admin can reserve on behalf of a user
        
        Policy: An admin can reserve on behalf of a user only if:
        - That user has at least one confirmed reservation already
        
        Args:
            person: Person object
            
        Returns:
            tuple: (can_reserve: bool, error_message: str)
        """
        # Check for confirmed reservations
        confirmed_reservations = HallReservation.objects.filter(
            person=person,
            reservation_status='confirmed'
        )
        
        if not confirmed_reservations.exists():
            return False, cls.ADMIN_NO_CONFIRMED_RESERVATION_ERROR
        
        return True, ""
    
    @classmethod
    def validate_reservation_request(cls, person, is_admin_created=False, requesting_user=None):
        """
        Main validation method for reservation requests
        
        Args:
            person: Person object for whom the reservation is being made
            is_admin_created: Boolean indicating if this is an admin-created reservation
            requesting_user: User object making the request (optional)
            
        Returns:
            tuple: (can_reserve: bool, error_message: str, validation_details: dict)
        """
        validation_details = {
            'person_id': person.national_id,
            'person_name': person.name,
            'is_admin_created': is_admin_created,
            'requesting_user': requesting_user.username if requesting_user else None,
            'reservation_summary': cls.get_person_reservation_summary(person)
        }
        
        if is_admin_created:
            # Admin reservation validation
            can_reserve, error_message = cls.can_admin_reserve_for(person)
            validation_details['validation_type'] = 'admin_reservation'
        else:
            # Regular user reservation validation
            can_reserve, error_message = cls.can_user_reserve(person)
            validation_details['validation_type'] = 'user_reservation'
        
        validation_details['can_reserve'] = can_reserve
        validation_details['error_message'] = error_message
        
        return can_reserve, error_message, validation_details
    
    @classmethod
    def get_person_reservation_summary(cls, person):
        """
        Get a summary of person's reservations by status
        
        Args:
            person: Person object
            
        Returns:
            dict: Summary of reservations by status
        """
        reservations = HallReservation.objects.filter(person=person)
        
        summary = {
            'total': reservations.count(),
            'confirmed': reservations.filter(reservation_status='confirmed').count(),
            'pending': reservations.filter(reservation_status='in_progress').count(),
            'cancelled': reservations.filter(reservation_status='cancelled').count(),
            'expired': reservations.filter(reservation_status='expired').count(),
        }
        
        # Add latest reservation info
        latest_reservation = reservations.order_by('-created_at').first()
        if latest_reservation:
            summary['latest_reservation'] = {
                'id': str(latest_reservation.id),
                'status': latest_reservation.reservation_status,
                'event_type': latest_reservation.event_type,
                'created_at': latest_reservation.created_at.isoformat(),
                'start_date': latest_reservation.start_date.isoformat(),
                'end_date': latest_reservation.end_date.isoformat(),
            }
        else:
            summary['latest_reservation'] = None
        
        return summary
    
    @classmethod
    def get_validation_message_for_user(cls, person, is_admin_created=False):
        """
        Get a user-friendly validation message
        
        Args:
            person: Person object
            is_admin_created: Boolean indicating if admin created
            
        Returns:
            str: User-friendly message explaining the validation result
        """
        can_reserve, error_message, details = cls.validate_reservation_request(
            person, is_admin_created
        )
        
        if can_reserve:
            if is_admin_created:
                return f"✅ Admin can create reservation for {person.name}"
            else:
                return f"✅ {person.name} can create a new reservation"
        else:
            summary = details['reservation_summary']
            if is_admin_created:
                return (f"❌ {error_message}\n"
                       f"User {person.name} has {summary['confirmed']} confirmed, "
                       f"{summary['pending']} pending reservations.")
            else:
                return (f"❌ {error_message}\n"
                       f"You have {summary['pending']} pending reservation(s). "
                       f"Please confirm or cancel them first.")
    
    @classmethod
    def check_legacy_reservation_exist(cls, person):
        """
        Legacy method compatibility - check if person has any active reservations
        
        Args:
            person: Person object
            
        Returns:
            bool: True if person has any active reservations
        """
        active_reservations = HallReservation.objects.filter(
            person=person,
            reservation_status__in=['in_progress', 'confirmed']
        )
        return active_reservations.exists()


# Convenience functions for backward compatibility
def can_user_reserve(person):
    """Convenience function for user reservation validation"""
    return ReservationPolicyValidator.can_user_reserve(person)


def can_admin_reserve_for(person):
    """Convenience function for admin reservation validation"""
    return ReservationPolicyValidator.can_admin_reserve_for(person)


def validate_reservation_request(person, is_admin_created=False, requesting_user=None):
    """Convenience function for full validation"""
    return ReservationPolicyValidator.validate_reservation_request(
        person, is_admin_created, requesting_user
    )
