from django.contrib.sitemaps import Sitemap
from django.urls import reverse
from django.conf import settings
from django.http import HttpResponse
from datetime import datetime

# Import Firebase modules with error handling
try:
    from firebase_admin import firestore
    # Initialize Firestore client
    db = firestore.client()
except Exception as e:
    print(f"Warning: Firebase not available for sitemaps: {str(e)}")
    db = None


class StaticViewSitemap(Sitemap):
    """Sitemap for static pages - ONLY WELCOME PAGE"""
    priority = 1.0  # Highest priority for welcome page
    changefreq = 'weekly'
    protocol = 'https'

    def items(self):
        # Return only the welcome page
        return [
            'welcome',
        ]

    def location(self, item):
        return reverse(item)

    def lastmod(self, item):
        # Return current date for static pages
        return datetime.now()


# COMMENTED OUT: NewsSitemap - Remove news from search results
# class NewsSitemap(Sitemap):
#     """Sitemap for individual news articles"""
#     priority = 0.6
#     changefreq = 'monthly'
#     protocol = 'https'
#
#     def items(self):
#         """Get all news articles from Firestore"""
#         # Return empty list if Firebase is not available
#         if db is None:
#             return []
#
#         try:
#             news_ref = db.collection('news_achievements').stream()
#             news_items = []
#
#             for doc in news_ref:
#                 data = doc.to_dict()
#                 # Add document ID for URL generation
#                 data['id'] = doc.id
#
#                 # Parse published date if available
#                 published_at = data.get('published_at', '')
#                 try:
#                     data['published_date'] = datetime.strptime(published_at, '%Y-%m-%d')
#                 except:
#                     data['published_date'] = datetime.now()
#
#                 news_items.append(data)
#
#             return news_items
#         except Exception as e:
#             # Return empty list if Firebase is not available
#             print(f"Error fetching news for sitemap: {str(e)}")
#             return []
#
#     def location(self, item):
#         return reverse('news_detail', args=[item['id']])
#
#     def lastmod(self, item):
#         return item.get('published_date', datetime.now())
#
#     def priority(self, item):
#         # Featured news gets higher priority
#         return 0.8 if item.get('is_featured', False) else 0.6


# Dictionary of sitemaps - ONLY STATIC (WELCOME PAGE)
sitemaps = {
    'static': StaticViewSitemap,
    # 'news': NewsSitemap,  # Removed news sitemap
}