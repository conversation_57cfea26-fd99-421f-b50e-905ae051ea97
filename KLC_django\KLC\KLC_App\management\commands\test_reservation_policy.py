"""
Django management command to test the new reservation policy logic
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import User
from KLC_App.models import Person, HallReservation
from KLC_App.reservation_policy import ReservationPolicyValidator
from django.utils import timezone
from datetime import timedelta


class Command(BaseCommand):
    help = 'Test the new reservation policy logic with various scenarios'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-test-data',
            action='store_true',
            help='Create test data for policy validation',
        )
        parser.add_argument(
            '--test-scenarios',
            action='store_true',
            help='Run all test scenarios',
        )
        parser.add_argument(
            '--person-id',
            type=str,
            help='Test specific person by national ID',
        )

    def handle(self, *args, **options):
        if options['create_test_data']:
            self._create_test_data()
        
        if options['test_scenarios']:
            self._run_test_scenarios()
        
        if options['person_id']:
            self._test_specific_person(options['person_id'])

    def _create_test_data(self):
        """Create test data for policy validation"""
        self.stdout.write("Creating test data...")
        
        # Create test persons
        test_persons = [
            {'national_id': '123456789', 'name': 'أحمد محمد', 'debts_amount_2024': 100},
            {'national_id': '987654321', 'name': 'فاطمة علي', 'debts_amount_2024': 50},
            {'national_id': '555666777', 'name': 'محمد خالد', 'debts_amount_2024': 300},
        ]
        
        for person_data in test_persons:
            person, created = Person.objects.get_or_create(
                national_id=person_data['national_id'],
                defaults={
                    'name': person_data['name'],
                    'debts_amount_2024': person_data['debts_amount_2024']
                }
            )
            if created:
                self.stdout.write(f"  ✅ Created person: {person.name}")
            else:
                self.stdout.write(f"  ℹ️ Person already exists: {person.name}")
        
        # Create test reservations
        person1 = Person.objects.get(national_id='123456789')
        person2 = Person.objects.get(national_id='987654321')
        
        # Person 1: Has a confirmed reservation
        confirmed_reservation, created = HallReservation.objects.get_or_create(
            person=person1,
            event_type='عرس',
            defaults={
                'full_name': person1.name,
                'phone_number': '0501234567',
                'start_date': timezone.now().date() + timedelta(days=30),
                'end_date': timezone.now().date() + timedelta(days=31),
                'reservation_status': 'confirmed',
                'admin_created': False
            }
        )
        if created:
            self.stdout.write(f"  ✅ Created confirmed reservation for {person1.name}")
        
        # Person 2: Has a pending reservation
        pending_reservation, created = HallReservation.objects.get_or_create(
            person=person2,
            event_type='خطوبة',
            defaults={
                'full_name': person2.name,
                'phone_number': '0509876543',
                'start_date': timezone.now().date() + timedelta(days=15),
                'end_date': timezone.now().date() + timedelta(days=16),
                'reservation_status': 'in_progress',
                'admin_created': False
            }
        )
        if created:
            self.stdout.write(f"  ✅ Created pending reservation for {person2.name}")
        
        self.stdout.write(self.style.SUCCESS("Test data created successfully!"))

    def _run_test_scenarios(self):
        """Run comprehensive test scenarios"""
        self.stdout.write("\n" + "="*60)
        self.stdout.write("RUNNING RESERVATION POLICY TEST SCENARIOS")
        self.stdout.write("="*60)
        
        # Test Scenario 1: User with no reservations
        self._test_scenario_1()
        
        # Test Scenario 2: User with pending reservation
        self._test_scenario_2()
        
        # Test Scenario 3: User with confirmed reservation
        self._test_scenario_3()
        
        # Test Scenario 4: Admin reservation for user with confirmed reservation
        self._test_scenario_4()
        
        # Test Scenario 5: Admin reservation for user without confirmed reservation
        self._test_scenario_5()

    def _test_scenario_1(self):
        """Test: User with no reservations should be able to reserve"""
        self.stdout.write("\n📋 Test Scenario 1: User with no reservations")
        
        person = Person.objects.get(national_id='555666777')
        can_reserve, error_message = ReservationPolicyValidator.can_user_reserve(person)
        
        self.stdout.write(f"  Person: {person.name}")
        self.stdout.write(f"  Reservations: {person.reservations.count()}")
        self.stdout.write(f"  Can reserve: {can_reserve}")
        self.stdout.write(f"  Message: {error_message or 'No restrictions'}")
        
        if can_reserve:
            self.stdout.write(self.style.SUCCESS("  ✅ PASS: User can make reservation"))
        else:
            self.stdout.write(self.style.ERROR("  ❌ FAIL: User should be able to make reservation"))

    def _test_scenario_2(self):
        """Test: User with pending reservation should NOT be able to reserve"""
        self.stdout.write("\n📋 Test Scenario 2: User with pending reservation")
        
        person = Person.objects.get(national_id='987654321')
        can_reserve, error_message = ReservationPolicyValidator.can_user_reserve(person)
        
        pending_count = person.reservations.filter(reservation_status='in_progress').count()
        
        self.stdout.write(f"  Person: {person.name}")
        self.stdout.write(f"  Pending reservations: {pending_count}")
        self.stdout.write(f"  Can reserve: {can_reserve}")
        self.stdout.write(f"  Message: {error_message or 'No restrictions'}")
        
        if not can_reserve:
            self.stdout.write(self.style.SUCCESS("  ✅ PASS: User correctly blocked from making reservation"))
        else:
            self.stdout.write(self.style.ERROR("  ❌ FAIL: User should be blocked from making reservation"))

    def _test_scenario_3(self):
        """Test: User with only confirmed reservations should be able to reserve"""
        self.stdout.write("\n📋 Test Scenario 3: User with confirmed reservation")
        
        person = Person.objects.get(national_id='123456789')
        can_reserve, error_message = ReservationPolicyValidator.can_user_reserve(person)
        
        confirmed_count = person.reservations.filter(reservation_status='confirmed').count()
        pending_count = person.reservations.filter(reservation_status='in_progress').count()
        
        self.stdout.write(f"  Person: {person.name}")
        self.stdout.write(f"  Confirmed reservations: {confirmed_count}")
        self.stdout.write(f"  Pending reservations: {pending_count}")
        self.stdout.write(f"  Can reserve: {can_reserve}")
        self.stdout.write(f"  Message: {error_message or 'No restrictions'}")
        
        if can_reserve:
            self.stdout.write(self.style.SUCCESS("  ✅ PASS: User can make new reservation"))
        else:
            self.stdout.write(self.style.ERROR("  ❌ FAIL: User should be able to make new reservation"))

    def _test_scenario_4(self):
        """Test: Admin reservation for user with confirmed reservation should work"""
        self.stdout.write("\n📋 Test Scenario 4: Admin reservation for user with confirmed reservation")
        
        person = Person.objects.get(national_id='123456789')
        can_reserve, error_message = ReservationPolicyValidator.can_admin_reserve_for(person)
        
        confirmed_count = person.reservations.filter(reservation_status='confirmed').count()
        
        self.stdout.write(f"  Person: {person.name}")
        self.stdout.write(f"  Confirmed reservations: {confirmed_count}")
        self.stdout.write(f"  Admin can reserve: {can_reserve}")
        self.stdout.write(f"  Message: {error_message or 'No restrictions'}")
        
        if can_reserve:
            self.stdout.write(self.style.SUCCESS("  ✅ PASS: Admin can make reservation for this user"))
        else:
            self.stdout.write(self.style.ERROR("  ❌ FAIL: Admin should be able to make reservation for this user"))

    def _test_scenario_5(self):
        """Test: Admin reservation for user without confirmed reservation should fail"""
        self.stdout.write("\n📋 Test Scenario 5: Admin reservation for user without confirmed reservation")
        
        person = Person.objects.get(national_id='555666777')
        can_reserve, error_message = ReservationPolicyValidator.can_admin_reserve_for(person)
        
        confirmed_count = person.reservations.filter(reservation_status='confirmed').count()
        
        self.stdout.write(f"  Person: {person.name}")
        self.stdout.write(f"  Confirmed reservations: {confirmed_count}")
        self.stdout.write(f"  Admin can reserve: {can_reserve}")
        self.stdout.write(f"  Message: {error_message or 'No restrictions'}")
        
        if not can_reserve:
            self.stdout.write(self.style.SUCCESS("  ✅ PASS: Admin correctly blocked from making reservation"))
        else:
            self.stdout.write(self.style.ERROR("  ❌ FAIL: Admin should be blocked from making reservation"))

    def _test_specific_person(self, national_id):
        """Test specific person by national ID"""
        try:
            person = Person.objects.get(national_id=national_id)
        except Person.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Person with ID {national_id} not found"))
            return
        
        self.stdout.write(f"\n📋 Testing person: {person.name} ({person.national_id})")
        
        # Get reservation summary
        summary = ReservationPolicyValidator.get_person_reservation_summary(person)
        
        self.stdout.write(f"  Total reservations: {summary['total']}")
        self.stdout.write(f"  Confirmed: {summary['confirmed']}")
        self.stdout.write(f"  Pending: {summary['pending']}")
        self.stdout.write(f"  Cancelled: {summary['cancelled']}")
        self.stdout.write(f"  Expired: {summary['expired']}")
        
        # Test user reservation
        can_user_reserve, user_error = ReservationPolicyValidator.can_user_reserve(person)
        self.stdout.write(f"\n  User can reserve: {can_user_reserve}")
        if user_error:
            self.stdout.write(f"  User error: {user_error}")
        
        # Test admin reservation
        can_admin_reserve, admin_error = ReservationPolicyValidator.can_admin_reserve_for(person)
        self.stdout.write(f"  Admin can reserve: {can_admin_reserve}")
        if admin_error:
            self.stdout.write(f"  Admin error: {admin_error}")
        
        # Show latest reservation
        if summary['latest_reservation']:
            latest = summary['latest_reservation']
            self.stdout.write(f"\n  Latest reservation:")
            self.stdout.write(f"    Event: {latest['event_type']}")
            self.stdout.write(f"    Status: {latest['status']}")
            self.stdout.write(f"    Date: {latest['start_date']} to {latest['end_date']}")
