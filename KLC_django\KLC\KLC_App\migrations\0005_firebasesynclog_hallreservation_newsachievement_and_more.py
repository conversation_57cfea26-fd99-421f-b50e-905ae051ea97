# Generated by Django 5.2.3 on 2025-07-29 13:07

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('KLC_App', '0004_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FirebaseSyncLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('collection_name', models.CharField(max_length=100, verbose_name='اسم المجموعة')),
                ('sync_type', models.CharField(choices=[('import', 'استيراد من Firebase'), ('export', 'تصدير إلى Firebase'), ('bidirectional', 'مزامنة ثنائية الاتجاه')], max_length=20, verbose_name='نوع المزامنة')),
                ('sync_status', models.CharField(choices=[('started', 'بدأت'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتملة'), ('failed', 'فشلت'), ('partial', 'مكتملة جزئياً')], default='started', max_length=20, verbose_name='حالة المزامنة')),
                ('records_processed', models.PositiveIntegerField(default=0, verbose_name='عدد السجلات المعالجة')),
                ('records_successful', models.PositiveIntegerField(default=0, verbose_name='عدد السجلات الناجحة')),
                ('records_failed', models.PositiveIntegerField(default=0, verbose_name='عدد السجلات الفاشلة')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='رسالة الخطأ')),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('initiated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='بدأ بواسطة')),
            ],
            options={
                'verbose_name': 'سجل مزامنة Firebase',
                'verbose_name_plural': 'سجلات مزامنة Firebase',
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='HallReservation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('full_name', models.CharField(max_length=200, verbose_name='الاسم الكامل')),
                ('phone_number', models.CharField(max_length=15, verbose_name='رقم الهاتف')),
                ('event_type', models.CharField(max_length=100, verbose_name='نوع المناسبة')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('reservation_status', models.CharField(choices=[('in_progress', 'قيد التنفيذ'), ('confirmed', 'مؤكد'), ('cancelled', 'ملغي'), ('expired', 'منتهي الصلاحية')], default='in_progress', max_length=20, verbose_name='حالة الحجز')),
                ('admin_created', models.BooleanField(default=False, verbose_name='تم إنشاؤه من قبل الإدارة')),
                ('firebase_doc_id', models.CharField(blank=True, max_length=100, null=True)),
                ('last_synced', models.DateTimeField(blank=True, null=True)),
                ('sync_status', models.CharField(choices=[('synced', 'متزامن'), ('pending', 'في الانتظار'), ('error', 'خطأ في المزامنة')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'حجز قاعة',
                'verbose_name_plural': 'حجوزات القاعات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='NewsAchievement',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255, verbose_name='عنوان الخبر')),
                ('description', models.TextField(verbose_name='وصف الخبر')),
                ('image_src', models.JSONField(blank=True, help_text='JSON containing firebase_url and/or local_url', null=True, verbose_name='الصورة الرئيسية')),
                ('additional_images', models.JSONField(blank=True, help_text='Array of image URLs', null=True, verbose_name='صور إضافية')),
                ('video_url', models.URLField(blank=True, null=True, verbose_name='رابط الفيديو')),
                ('news_type', models.CharField(choices=[('news', 'أخبار'), ('achievements', 'إنجازات'), ('projects', 'مشاريع'), ('announcements', 'إعلانات')], default='news', max_length=50, verbose_name='نوع الخبر')),
                ('is_featured', models.BooleanField(default=False, help_text='سيظهر كخبر رئيسي في الصفحة الرئيسية', verbose_name='خبر مميز')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمد للنشر')),
                ('published_at', models.DateField(blank=True, null=True, verbose_name='تاريخ النشر')),
                ('firebase_doc_id', models.CharField(blank=True, max_length=100, null=True)),
                ('last_synced', models.DateTimeField(blank=True, null=True)),
                ('sync_status', models.CharField(choices=[('synced', 'متزامن'), ('pending', 'في الانتظار'), ('error', 'خطأ في المزامنة')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'خبر أو إنجاز',
                'verbose_name_plural': 'الأخبار والإنجازات',
                'ordering': ['-published_at', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Person',
            fields=[
                ('national_id', models.CharField(help_text='رقم الهوية الوطنية للمواطن', max_length=20, primary_key=True, serialize=False, unique=True, verbose_name='رقم الهوية الوطنية')),
                ('name', models.CharField(help_text='الاسم الثلاثي للمواطن', max_length=200, verbose_name='الاسم الكامل')),
                ('debts_amount_2024', models.DecimalField(decimal_places=2, default=0.0, help_text='المبلغ المستحق على المواطن بالشيقل', max_digits=10, verbose_name='المديونية لعام 2024')),
                ('firebase_doc_id', models.CharField(blank=True, max_length=50, null=True)),
                ('last_synced', models.DateTimeField(blank=True, null=True)),
                ('sync_status', models.CharField(choices=[('synced', 'متزامن'), ('pending', 'في الانتظار'), ('error', 'خطأ في المزامنة')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'مواطن',
                'verbose_name_plural': 'المواطنون',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='SuggestionComplaint',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('full_name', models.CharField(default='بدون إسم', max_length=200, verbose_name='الاسم الكامل')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('firebase_doc_id', models.CharField(blank=True, max_length=100, null=True)),
                ('last_synced', models.DateTimeField(blank=True, null=True)),
                ('sync_status', models.CharField(choices=[('synced', 'متزامن'), ('pending', 'في الانتظار'), ('error', 'خطأ في المزامنة')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('person', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='suggestions', to='KLC_App.person', verbose_name='المواطن')),
            ],
            options={
                'verbose_name': 'اقتراح أو شكوى',
                'verbose_name_plural': 'الاقتراحات والشكاوى',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True, verbose_name='المفتاح')),
                ('value', models.TextField(verbose_name='القيمة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('firebase_doc_id', models.CharField(blank=True, max_length=100, null=True)),
                ('last_synced', models.DateTimeField(blank=True, null=True)),
                ('sync_status', models.CharField(choices=[('synced', 'متزامن'), ('pending', 'في الانتظار'), ('error', 'خطأ في المزامنة')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'إعداد النظام',
                'verbose_name_plural': 'إعدادات النظام',
                'ordering': ['key'],
            },
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('full_name', models.CharField(max_length=200, verbose_name='الاسم الكامل')),
                ('phone_number', models.CharField(max_length=15, verbose_name='رقم الهاتف')),
                ('transaction_type', models.CharField(choices=[('route_marking', 'طلب تعليم طريق'), ('site_plan_guide', 'طلب دليل مخطط موقع'), ('building_permit', 'طلب ترخيص مبنى'), ('water_line_noc', 'طلب عدم ممانعة لتقديم خدمة خط مياه'), ('electricity_line_noc', 'طلب عدم ممانعة لمد خط كهرباء'), ('proof_of_residence', 'طلب إثبات سكن'), ('employment_proof', 'طلب إثبات عمل'), ('sale_transaction_palestinian', 'طلب معاملات صفقة بيع (لحملة الهوية الفلسطينية) وصف قطعة، براءة ذمة'), ('sale_transaction_jerusalem', 'طلب معاملات صفقة بيع (لحملة هوية القدس) وصف قطعة، براءة ذمة وتصنيف'), ('clearance_kushan', 'طلب معاملة براءة ذمة لاستخراج الكوشان'), ('other', 'طلب معاملة أخرى او استفسار')], max_length=50, verbose_name='نوع الطلب')),
                ('additional_notes', models.TextField(blank=True, default='لا يوجد ملاحظات إضافية', verbose_name='ملاحظات إضافية')),
                ('transaction_status', models.CharField(choices=[('in_progress', 'قيد التنفيذ'), ('done', 'مكتمل'), ('cancelled', 'ملغي')], default='in_progress', max_length=20, verbose_name='حالة الطلب')),
                ('receipt_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الإيصال')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإكمال')),
                ('firebase_doc_id', models.CharField(blank=True, max_length=100, null=True)),
                ('last_synced', models.DateTimeField(blank=True, null=True)),
                ('sync_status', models.CharField(choices=[('synced', 'متزامن'), ('pending', 'في الانتظار'), ('error', 'خطأ في المزامنة')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('person', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='KLC_App.person', verbose_name='المواطن')),
            ],
            options={
                'verbose_name': 'طلب معاملة',
                'verbose_name_plural': 'طلبات المعاملات',
                'ordering': ['-created_at'],
                'unique_together': {('person', 'transaction_type')},
            },
        ),
        migrations.DeleteModel(
            name='Advertisement',
        ),
        migrations.AddField(
            model_name='hallreservation',
            name='person',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reservations', to='KLC_App.person', verbose_name='المواطن'),
        ),
    ]
