{% extends "admin/base_site.html" %}
{% load i18n static admin_urls %}

{% block extrahead %}
{{ block.super }}
<script src="{% static 'admin/js/jquery.init.js' %}"></script>
<style>
.dashboard .module {
    float: right;
    width: 48%;
    margin: 1%;
}

.dashboard .module table {
    width: 100%;
}

.dashboard .module table th {
    text-align: right;
}

.dashboard .module table td {
    text-align: right;
    padding: 8px 12px;
}

.sync-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.sync-status.synced {
    background: #d4edda;
    color: #155724;
}

.sync-status.pending {
    background: #fff3cd;
    color: #856404;
}

.sync-status.error {
    background: #f8d7da;
    color: #721c24;
}

.quick-actions {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
}

.quick-actions h3 {
    margin-top: 0;
    color: #2c5aa0;
}

.action-button {
    display: inline-block;
    margin: 5px 10px 5px 0;
    padding: 8px 16px;
    background: #2c5aa0;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
}

.action-button:hover {
    background: #1e3a8a;
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.stat-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #2c5aa0;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
    margin-top: 5px;
}
</style>
{% endblock %}

{% block content %}
<div id="content-main">
    {% if app_list %}
        <!-- Quick Stats Section -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="persons-count">-</div>
                <div class="stat-label">إجمالي المواطنين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="reservations-count">-</div>
                <div class="stat-label">حجوزات القاعات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="transactions-count">-</div>
                <div class="stat-label">طلبات المعاملات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="suggestions-count">-</div>
                <div class="stat-label">الاقتراحات والشكاوى</div>
            </div>
        </div>

        <!-- Quick Actions Section -->
        <div class="quick-actions">
            <h3>إجراءات سريعة</h3>
            <a href="{% url 'admin:KLC_App_person_add' %}" class="action-button">إضافة مواطن جديد</a>
            <a href="{% url 'admin:KLC_App_hallreservation_add' %}" class="action-button">إضافة حجز قاعة</a>
            <a href="{% url 'admin:KLC_App_transaction_add' %}" class="action-button">إضافة طلب معاملة</a>
            <a href="{% url 'admin:KLC_App_newsachievement_add' %}" class="action-button">إضافة خبر</a>
            <a href="#" onclick="syncFirebase()" class="action-button">مزامنة Firebase</a>
        </div>

        <!-- Firebase Sync Status -->
        <div class="module">
            <h2>حالة مزامنة Firebase</h2>
            <table>
                <thead>
                    <tr>
                        <th>المجموعة</th>
                        <th>الحالة</th>
                        <th>آخر مزامنة</th>
                        <th>السجلات</th>
                    </tr>
                </thead>
                <tbody id="sync-status-table">
                    <tr>
                        <td colspan="4">جاري تحميل حالة المزامنة...</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Standard Django Admin App List -->
        {% for app in app_list %}
            <div class="module">
                <h2>
                    <a href="{{ app.app_url }}" class="section" title="{% blocktrans with name=app.name %}Models in the {{ name }} application{% endblocktrans %}">
                        {{ app.name }}
                    </a>
                </h2>
                <table>
                    {% for model in app.models %}
                        <tr class="model-{{ model.object_name|lower }}">
                            {% if model.admin_url %}
                                <th scope="row"><a href="{{ model.admin_url }}">{{ model.name }}</a></th>
                            {% else %}
                                <th scope="row">{{ model.name }}</th>
                            {% endif %}

                            {% if model.add_url %}
                                <td><a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a></td>
                            {% else %}
                                <td>&nbsp;</td>
                            {% endif %}

                            {% if model.admin_url %}
                                {% if model.view_only %}
                                    <td><a href="{{ model.admin_url }}" class="viewlink">{% trans 'View' %}</a></td>
                                {% else %}
                                    <td><a href="{{ model.admin_url }}" class="changelink">{% trans 'Change' %}</a></td>
                                {% endif %}
                            {% else %}
                                <td>&nbsp;</td>
                            {% endif %}
                        </tr>
                    {% endfor %}
                </table>
            </div>
        {% endfor %}
    {% else %}
        <p>{% trans "You don't have permission to view or edit anything." %}</p>
    {% endif %}
</div>

<script>
// Load statistics
function loadStats() {
    // This would typically make AJAX calls to get real-time stats
    // For now, we'll use placeholder values
    document.getElementById('persons-count').textContent = '367';
    document.getElementById('reservations-count').textContent = '1';
    document.getElementById('transactions-count').textContent = '0';
    document.getElementById('suggestions-count').textContent = '0';
}

// Load Firebase sync status
function loadSyncStatus() {
    // This would make an AJAX call to get sync status
    const tbody = document.getElementById('sync-status-table');
    tbody.innerHTML = `
        <tr>
            <td>persons</td>
            <td><span class="sync-status pending">في الانتظار</span></td>
            <td>لم يتم المزامنة بعد</td>
            <td>367</td>
        </tr>
        <tr>
            <td>hall_reservations</td>
            <td><span class="sync-status pending">في الانتظار</span></td>
            <td>لم يتم المزامنة بعد</td>
            <td>1</td>
        </tr>
        <tr>
            <td>transactions</td>
            <td><span class="sync-status pending">في الانتظار</span></td>
            <td>لم يتم المزامنة بعد</td>
            <td>0</td>
        </tr>
    `;
}

// Sync Firebase data
function syncFirebase() {
    if (confirm('هل تريد مزامنة جميع البيانات مع Firebase؟ قد تستغرق هذه العملية بعض الوقت.')) {
        alert('سيتم تنفيذ هذه الميزة قريباً. يرجى استخدام أوامر الإدارة في الوقت الحالي.');
    }
}

// Load data when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadStats();
    loadSyncStatus();
});
</script>
{% endblock %}
