"""
Modern Admin Panel Views
Provides a comprehensive admin interface with role-based permissions
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Count, Sum, Q
from django.utils import timezone
from django.core.paginator import Paginator
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from datetime import datetime, timedelta
import json

from .models import AdminProfile, Person, HallReservation, Transaction, SuggestionComplaint, NewsAchievement
from .admin_auth import admin_required, admin_login_required, AdminAuthenticationService, get_admin_context


def admin_login_view(request):
    """Admin login page"""
    if request.user.is_authenticated:
        try:
            AdminProfile.objects.get(user=request.user)
            return redirect('admin_panel_dashboard')
        except AdminProfile.DoesNotExist:
            logout(request)
    
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        
        success, user, message = AdminAuthenticationService.authenticate_admin(
            username, password, request
        )
        
        if success:
            login(request, user)
            messages.success(request, message)
            
            # Redirect to intended page or dashboard
            next_url = request.GET.get('next', 'admin_panel_dashboard')
            return redirect(next_url)
        else:
            messages.error(request, message)
    
    return render(request, 'KLC_App/admin_panel/login.html')


@admin_login_required
def admin_logout_view(request):
    """Admin logout"""
    logout(request)
    messages.success(request, 'تم تسجيل الخروج بنجاح')
    return redirect('admin_login_view')


@admin_login_required
def admin_dashboard(request):
    """Main admin dashboard with statistics and quick actions"""
    context = get_admin_context(request)
    
    # Get current date for filtering
    today = timezone.now().date()
    current_month = today.replace(day=1)
    current_year = today.replace(month=1, day=1)
    
    # Basic statistics
    stats = {
        'total_users': Person.objects.count(),
        'total_reservations': HallReservation.objects.count(),
        'pending_reservations': HallReservation.objects.filter(reservation_status='in_progress').count(),
        'confirmed_reservations': HallReservation.objects.filter(reservation_status='confirmed').count(),
        'total_transactions': Transaction.objects.count(),
        'pending_transactions': Transaction.objects.filter(transaction_status='in_progress').count(),
        'completed_transactions': Transaction.objects.filter(transaction_status='done').count(),
        'total_complaints': SuggestionComplaint.objects.count(),
        'unread_complaints': SuggestionComplaint.objects.filter(is_read=False).count(),
    }
    
    # Monthly statistics
    monthly_stats = {
        'reservations_this_month': HallReservation.objects.filter(
            created_at__gte=current_month
        ).count(),
        'transactions_this_month': Transaction.objects.filter(
            created_at__gte=current_month
        ).count(),
        'revenue_this_month': HallReservation.objects.filter(
            created_at__gte=current_month,
            reservation_status='confirmed'
        ).aggregate(
            total=Sum('person__debts_amount_2024')
        )['total'] or 0,
    }
    
    # Recent activities
    recent_reservations = HallReservation.objects.select_related('person').order_by('-created_at')[:5]
    recent_transactions = Transaction.objects.select_related('person').order_by('-created_at')[:5]
    recent_complaints = SuggestionComplaint.objects.select_related('person').order_by('-created_at')[:5]
    
    # Chart data for reservations by status
    reservation_chart_data = {
        'labels': ['قيد التنفيذ', 'مؤكد', 'ملغي', 'منتهي الصلاحية'],
        'data': [
            HallReservation.objects.filter(reservation_status='in_progress').count(),
            HallReservation.objects.filter(reservation_status='confirmed').count(),
            HallReservation.objects.filter(reservation_status='cancelled').count(),
            HallReservation.objects.filter(reservation_status='expired').count(),
        ]
    }
    
    # Transaction chart data
    transaction_chart_data = {
        'labels': ['قيد التنفيذ', 'مكتمل', 'ملغي'],
        'data': [
            Transaction.objects.filter(transaction_status='in_progress').count(),
            Transaction.objects.filter(transaction_status='done').count(),
            Transaction.objects.filter(transaction_status='cancelled').count(),
        ]
    }
    
    context.update({
        'active_page': 'dashboard',
        'stats': stats,
        'monthly_stats': monthly_stats,
        'recent_reservations': recent_reservations,
        'recent_transactions': recent_transactions,
        'recent_complaints': recent_complaints,
        'reservation_chart_data': json.dumps(reservation_chart_data),
        'transaction_chart_data': json.dumps(transaction_chart_data),
    })
    
    return render(request, 'KLC_App/admin_panel/dashboard.html', context)


@admin_required(permission='manage_admins')
def admin_management(request):
    """Admin management interface"""
    context = get_admin_context(request)
    
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'create_admin':
            username = request.POST.get('username')
            password = request.POST.get('password')
            email = request.POST.get('email', '')
            first_name = request.POST.get('first_name', '')
            last_name = request.POST.get('last_name', '')
            
            # Get permissions
            permissions = {
                'full_permissions': request.POST.get('full_permissions') == 'on',
                'can_manage_halls': request.POST.get('can_manage_halls') == 'on',
                'can_manage_users': request.POST.get('can_manage_users') == 'on',
                'can_approve_reservations': request.POST.get('can_approve_reservations') == 'on',
                'can_manage_transactions': request.POST.get('can_manage_transactions') == 'on',
                'can_view_reports': request.POST.get('can_view_reports') == 'on',
                'can_manage_admins': request.POST.get('can_manage_admins') == 'on',
                'can_manage_news': request.POST.get('can_manage_news') == 'on',
                'can_handle_complaints': request.POST.get('can_handle_complaints') == 'on',
            }
            
            success, admin_profile, message = AdminAuthenticationService.create_admin_user(
                username=username,
                password=password,
                email=email,
                permissions=permissions,
                created_by=request.user
            )
            
            if success:
                # Update user's name
                admin_profile.user.first_name = first_name
                admin_profile.user.last_name = last_name
                admin_profile.user.save()
                
                messages.success(request, message)
            else:
                messages.error(request, message)
        
        elif action == 'update_permissions':
            admin_id = request.POST.get('admin_id')
            try:
                admin_profile = AdminProfile.objects.get(id=admin_id)
                
                permissions = {
                    'full_permissions': request.POST.get('full_permissions') == 'on',
                    'can_manage_halls': request.POST.get('can_manage_halls') == 'on',
                    'can_manage_users': request.POST.get('can_manage_users') == 'on',
                    'can_approve_reservations': request.POST.get('can_approve_reservations') == 'on',
                    'can_manage_transactions': request.POST.get('can_manage_transactions') == 'on',
                    'can_view_reports': request.POST.get('can_view_reports') == 'on',
                    'can_manage_admins': request.POST.get('can_manage_admins') == 'on',
                    'can_manage_news': request.POST.get('can_manage_news') == 'on',
                    'can_handle_complaints': request.POST.get('can_handle_complaints') == 'on',
                }
                
                success, message = AdminAuthenticationService.update_admin_permissions(
                    admin_profile, permissions
                )
                
                if success:
                    messages.success(request, message)
                else:
                    messages.error(request, message)
                    
            except AdminProfile.DoesNotExist:
                messages.error(request, 'المشرف غير موجود')
        
        elif action == 'deactivate_admin':
            admin_id = request.POST.get('admin_id')
            try:
                admin_profile = AdminProfile.objects.get(id=admin_id)
                
                # Prevent self-deactivation
                if admin_profile.user == request.user:
                    messages.error(request, 'لا يمكنك إلغاء تفعيل حسابك الخاص')
                else:
                    success, message = AdminAuthenticationService.delete_admin_user(
                        admin_profile, request.user
                    )
                    
                    if success:
                        messages.success(request, message)
                    else:
                        messages.error(request, message)
                        
            except AdminProfile.DoesNotExist:
                messages.error(request, 'المشرف غير موجود')
        
        return redirect('admin_management')
    
    # Get all admin profiles
    admin_profiles = AdminProfile.objects.select_related('user').filter(
        user__is_active=True
    ).order_by('-created_at')
    
    context.update({
        'active_page': 'admin_management',
        'admin_profiles': admin_profiles,
    })
    
    return render(request, 'KLC_App/admin_panel/admin_management.html', context)


@admin_required(permission='manage_users')
def user_management(request):
    """User management interface"""
    context = get_admin_context(request)
    
    # Get search and filter parameters
    search_query = request.GET.get('search', '')
    debt_filter = request.GET.get('debt_filter', '')
    
    # Base queryset
    users = Person.objects.all()
    
    # Apply search
    if search_query:
        users = users.filter(
            Q(name__icontains=search_query) |
            Q(national_id__icontains=search_query)
        )
    
    # Apply debt filter
    if debt_filter == 'with_debt':
        users = users.filter(debts_amount_2024__gt=0)
    elif debt_filter == 'no_debt':
        users = users.filter(debts_amount_2024=0)
    elif debt_filter == 'high_debt':
        users = users.filter(debts_amount_2024__gt=240)
    
    # Pagination
    paginator = Paginator(users.order_by('name'), 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context.update({
        'active_page': 'user_management',
        'page_obj': page_obj,
        'search_query': search_query,
        'debt_filter': debt_filter,
        'total_users': users.count(),
    })
    
    return render(request, 'KLC_App/admin_panel/user_management.html', context)


# Placeholder views for remaining features
@admin_required(permission='approve_reservations')
def reservation_management(request):
    """Reservation management interface"""
    context = get_admin_context(request)
    context.update({
        'active_page': 'reservation_management',
    })
    return render(request, 'KLC_App/admin_panel/coming_soon.html', context)


@admin_required(permission='manage_transactions')
def transaction_management(request):
    """Transaction management interface"""
    context = get_admin_context(request)
    context.update({
        'active_page': 'transaction_management',
    })
    return render(request, 'KLC_App/admin_panel/coming_soon.html', context)


@admin_required(permission='handle_complaints')
def complaint_management(request):
    """Complaint management interface"""
    context = get_admin_context(request)
    context.update({
        'active_page': 'complaint_management',
    })
    return render(request, 'KLC_App/admin_panel/coming_soon.html', context)


@admin_required(permission='manage_news')
def news_management(request):
    """News management interface"""
    context = get_admin_context(request)
    context.update({
        'active_page': 'news_management',
    })
    return render(request, 'KLC_App/admin_panel/coming_soon.html', context)


@admin_required(permission='view_reports')
def reports(request):
    """Reports and analytics interface"""
    context = get_admin_context(request)
    context.update({
        'active_page': 'reports',
    })
    return render(request, 'KLC_App/admin_panel/coming_soon.html', context)
