# Generated by Django 5.2.3 on 2025-07-29 13:48

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('KLC_App', '0005_firebasesynclog_hallreservation_newsachievement_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AdminProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_permissions', models.BooleanField(default=False, help_text='منح جميع الصلاحيات للمشرف', verbose_name='صلاحيات كاملة')),
                ('can_manage_halls', models.BooleanField(default=False, help_text='إنشاء وتعديل وحذف القاعات', verbose_name='إدارة القاعات')),
                ('can_manage_users', models.BooleanField(default=False, help_text='عرض وتعديل بيانات المستخدمين', verbose_name='إدارة المستخدمين')),
                ('can_approve_reservations', models.BooleanField(default=False, help_text='الموافقة أو رفض طلبات حجز القاعات', verbose_name='الموافقة على الحجوزات')),
                ('can_manage_transactions', models.BooleanField(default=False, help_text='معالجة المعاملات والمدفوعات', verbose_name='إدارة المعاملات')),
                ('can_view_reports', models.BooleanField(default=False, help_text='الوصول إلى التقارير والإحصائيات', verbose_name='عرض التقارير')),
                ('can_manage_admins', models.BooleanField(default=False, help_text='إنشاء وتعديل وحذف حسابات المشرفين', verbose_name='إدارة المشرفين')),
                ('can_manage_news', models.BooleanField(default=False, help_text='إنشاء وتعديل ونشر الأخبار', verbose_name='إدارة الأخبار')),
                ('can_handle_complaints', models.BooleanField(default=False, help_text='عرض ومعالجة الشكاوي والاقتراحات', verbose_name='معالجة الشكاوي')),
                ('phone_number', models.CharField(blank=True, max_length=15, null=True, verbose_name='رقم الهاتف')),
                ('department', models.CharField(blank=True, max_length=100, null=True, verbose_name='القسم')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='آخر عنوان IP')),
                ('failed_login_attempts', models.PositiveIntegerField(default=0, verbose_name='محاولات تسجيل الدخول الفاشلة')),
                ('account_locked_until', models.DateTimeField(blank=True, null=True, verbose_name='مقفل حتى')),
                ('two_factor_enabled', models.BooleanField(default=False, verbose_name='المصادقة الثنائية مفعلة')),
                ('two_factor_secret', models.CharField(blank=True, max_length=32, null=True, verbose_name='مفتاح المصادقة الثنائية')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_admins', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('user', models.OneToOneField(help_text='حساب المستخدم المرتبط بالمشرف', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'ملف المشرف',
                'verbose_name_plural': 'ملفات المشرفين',
                'ordering': ['-created_at'],
            },
        ),
    ]
