{% extends 'KLC_App/admin_panel/base.html' %}

{% block title %}إدارة المستخدمين{% endblock %}
{% block page_title %}إدارة المستخدمين{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="flex items-center justify-between mb-6">
    <div>
        <h1 class="text-2xl font-bold text-gray-900">إدارة المستخدمين</h1>
        <p class="text-gray-600">عرض وإدارة بيانات المواطنين المسجلين</p>
    </div>
    <div class="text-sm text-gray-500">
        إجمالي المستخدمين: {{ total_users }}
    </div>
</div>

<!-- Search and Filters -->
<div class="bg-white rounded-xl card-shadow p-6 mb-6">
    <form method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
            <input 
                type="text" 
                name="search" 
                value="{{ search_query }}"
                placeholder="البحث بالاسم أو رقم الهوية"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500"
            >
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">فلترة حسب المديونية</label>
            <select name="debt_filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                <option value="">جميع المستخدمين</option>
                <option value="with_debt" {% if debt_filter == 'with_debt' %}selected{% endif %}>لديهم مديونية</option>
                <option value="no_debt" {% if debt_filter == 'no_debt' %}selected{% endif %}>بدون مديونية</option>
                <option value="high_debt" {% if debt_filter == 'high_debt' %}selected{% endif %}>مديونية عالية (أكثر من 240)</option>
            </select>
        </div>
        
        <div class="flex items-end">
            <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-search ml-1"></i>
                بحث
            </button>
        </div>
    </form>
</div>

<!-- Users Table -->
<div class="bg-white rounded-xl card-shadow overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">قائمة المستخدمين</h2>
    </div>
    
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الهوية</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاسم</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المديونية 2024</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحجوزات</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المعاملات</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for user in page_obj %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {{ user.national_id }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">{{ user.name }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                            {% if user.debts_amount_2024 > 0 %}
                                <span class="{% if user.debts_amount_2024 > 240 %}text-red-600 font-semibold{% else %}text-yellow-600{% endif %}">
                                    {{ user.debts_amount_2024 }} ₪
                                </span>
                            {% else %}
                                <span class="text-green-600">0 ₪</span>
                            {% endif %}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                                {{ user.reservations.count }} حجز
                            </span>
                            {% if user.reservations.filter.reservation_status='in_progress'.count > 0 %}
                                <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs">
                                    {{ user.reservations.filter.reservation_status='in_progress'.count }} في الانتظار
                                </span>
                            {% endif %}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">
                            {{ user.transactions.count }} معاملة
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button onclick="viewUserDetails('{{ user.national_id }}')" class="text-primary-600 hover:text-primary-900" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="editUserDebt('{{ user.national_id }}')" class="text-green-600 hover:text-green-900" title="تعديل المديونية">
                                <i class="fas fa-dollar-sign"></i>
                            </button>
                            <button onclick="viewUserHistory('{{ user.national_id }}')" class="text-blue-600 hover:text-blue-900" title="عرض السجل">
                                <i class="fas fa-history"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                        لا توجد نتائج للبحث
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
                عرض {{ page_obj.start_index }} إلى {{ page_obj.end_index }} من {{ page_obj.paginator.count }} نتيجة
            </div>
            
            <div class="flex items-center space-x-2 space-x-reverse">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if debt_filter %}&debt_filter={{ debt_filter }}{% endif %}" 
                       class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50">
                        السابق
                    </a>
                {% endif %}
                
                <span class="px-3 py-2 text-sm text-gray-700 bg-primary-50 border border-primary-200 rounded-lg">
                    صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                </span>
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if debt_filter %}&debt_filter={{ debt_filter }}{% endif %}" 
                       class="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50">
                        التالي
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    function viewUserDetails(nationalId) {
        // TODO: Implement user details modal
        alert(`عرض تفاصيل المستخدم: ${nationalId}`);
    }
    
    function editUserDebt(nationalId) {
        // TODO: Implement debt editing modal
        alert(`تعديل مديونية المستخدم: ${nationalId}`);
    }
    
    function viewUserHistory(nationalId) {
        // TODO: Implement user history modal
        alert(`عرض سجل المستخدم: ${nationalId}`);
    }
</script>
{% endblock %}
