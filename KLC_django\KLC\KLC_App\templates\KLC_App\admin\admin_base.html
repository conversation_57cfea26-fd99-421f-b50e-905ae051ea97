{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>{% block title %}لوحة تحكم الإدارة{% endblock %}</title>
    <!-- Force light mode by default -->
    <script>
      // Set light mode immediately before any other scripts run
      document.documentElement.setAttribute('data-theme', 'light');
      localStorage.setItem('theme', 'light');
    </script>

    <link
      rel="icon"
      type="image/x-icon"
      href="{% static 'images/logo.png' %}"
    />

    <!-- Core CSS for admin Dashboard -->
    <link href="{% static 'css/admin_dashboard.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Theme CSS -->
    <link href="{% static 'css/theme.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Enhanced Dark Mode CSS -->
    <link href="{% static 'css/dark_mode_enhanced.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Professional Admin UI CSS -->
    <link href="{% static 'css/professional_admin.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Admin Backgrounds CSS -->
    <link href="{% static 'css/admin_backgrounds.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Professional Forms CSS -->
    <link href="{% static 'css/professional-forms.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Modal Fix CSS -->
    <link href="{% static 'css/modal-fix.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Core js for admin Dashboard -->
    <script src="{% static 'js/admin_dashboard.js' %}?v={{ STATIC_VERSION }}" defer></script>
    <!-- Theme JS -->
    <script src="{% static 'js/theme.js' %}?v={{ STATIC_VERSION }}" defer></script>
    <script src="{% static 'js/footer.js' %}?v={{ STATIC_VERSION }}"></script>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&family=Tajawal:wght@400;700&family=Noto+Kufi+Arabic:wght@400;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', 'Tajawal', 'Noto Kufi Arabic', sans-serif;
            background: linear-gradient(135deg, #f6f8fa 0%, #e9efff 100%);
            min-height: 100vh;
            transition: background 0.3s;
        }
        .sidebar {
            background: #22304a;
            min-height: 100vh;
            color: #fff;
            box-shadow: 2px 0 12px rgba(30,41,59,0.08);
            transition: all 0.3s ease;
        }
        .sidebar .navbar-brand {
            color: #fff;
            font-size: 1.5rem;
            padding: 1rem;
            display: flex;
            align-items: center;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: #fff;
        }
        .sidebar .nav-link i {
            margin-left: 0.75rem;
            width: 20px;
            text-align: center;
        }
        .main-content {
            padding: 2rem;
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e0e7ef;
        }
        .section-header h2 {
            margin: 0;
            font-size: 1.5rem;
            color: #22304a;
        }
        .card {
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 4px 12px rgba(30,41,59,0.05);
            margin-bottom: 1.5rem;
            overflow: hidden;
        }
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        .theme-toggle {
            position: fixed;
            bottom: 32px;
            left: 32px;
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            box-shadow: 0 2px 8px rgba(37,99,235,0.12);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            z-index: 999;
            transition: background 0.2s;
        }
        .theme-toggle:hover {
            background: #1d4ed8;
        }
        /* Top cards (dashboard summary) */
        .card.shadow-sm.h-100 {
            background: linear-gradient(135deg, #f3f6fb 60%, #e0e7ef 100%);
            border: none;
            box-shadow: 0 2px 16px rgba(37,99,235,0.07);
            transition: background 0.3s, color 0.3s;
        }
        .card .fa-2x {
            filter: drop-shadow(0 2px 6px rgba(37,99,235,0.10));
        }

        /* Mobile Responsive Styles */
        .mobile-navbar {
            background: #22304a;
            color: #fff;
            position: sticky;
            top: 0;
            z-index: 1020;
            width: 100%;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .mobile-navbar .navbar-brand {
            color: #fff;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
        }

        .navbar-toggler {
            background: transparent;
            border: none;
            color: #fff;
            font-size: 1.5rem;
            padding: 0.25rem 0.75rem;
            cursor: pointer;
        }

        .mobile-sidebar-header {
            display: flex;
            justify-content: flex-end;
            padding: 1rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .btn-close-sidebar {
            background: rgba(255,255,255,0.1);
            border: none;
            color: #fff;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @media (max-width: 767.98px) {
            .sidebar {
                position: fixed;
                top: 0;
                right: -100%;
                width: 80%;
                height: 100vh;
                z-index: 1030;
                transition: right 0.3s ease;
                overflow-y: auto;
            }

            .sidebar.show {
                right: 0;
            }

            .main-content {
                padding: 1rem;
            }

            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .section-header > div {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                width: 100%;
            }

            .section-header .btn {
                flex: 1;
                white-space: nowrap;
            }

            .theme-toggle {
                bottom: 20px;
                left: 20px;
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
            }

            .table-responsive {
                overflow-x: auto;
            }

            .action-buttons {
                flex-wrap: wrap;
                justify-content: center;
            }

            .action-buttons .btn {
                margin-bottom: 0.25rem;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>

<body class="{% block body_class %}{% endblock %}">
    <div class="container-fluid">
        <div class="row">
            <!-- Mobile Navbar -->
            <div class="d-md-none mobile-navbar">
                <div class="d-flex justify-content-between align-items-center p-3">
                    <button class="navbar-toggler" type="button" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="navbar-brand">
                        <img src="{% static 'images/logo.png' %}" alt="Logo" class="me-2" style="height: 40px;">
                        <span>لوحة الإدارة</span>
                    </div>
                    <a href="{% url 'admin_logout' %}" class="btn btn-sm btn-outline-light">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>

            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar" id="sidebar">
                <div class="position-sticky">
                    <div class="navbar-brand mb-4 d-none d-md-flex">
                        <img src="{% static 'images/logo.png' %}" alt="Logo" class="me-2" style="height: 45px;">
                        لوحة الإدارة
                    </div>
                    <div class="d-md-none mobile-sidebar-header">
                        <button class="btn btn-sm btn-close-sidebar" id="closeSidebar">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if active_page == 'dashboard' %}active{% endif %}" href="{% url 'admin_dashboard' %}">
                                <i class="fas fa-chart-line"></i> الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if active_page == 'users' %}active{% endif %}" href="{% url 'admin_users' %}">
                                <i class="fas fa-users-cog"></i> إدارة المستخدمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if active_page == 'reservations' %}active{% endif %}" href="{% url 'admin_reservations' %}">
                                <i class="fas fa-calendar-alt"></i> حجوزات القاعة
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if active_page == 'transactions' %}active{% endif %}" href="{% url 'admin_transactions' %}">
                                <i class="fas fa-tasks"></i> الطلبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if active_page == 'suggestions' %}active{% endif %}" href="{% url 'admin_suggestions' %}">
                                <i class="fas fa-comment-alt"></i> الشكاوي والاقتراحات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if active_page == 'statistics' %}active{% endif %}" href="{% url 'transaction_statistics' %}">
                                <i class="fas fa-chart-bar"></i>
                                إحصائيات الطلبات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if active_page == 'news' %}active{% endif %}" href="{% url 'admin_news' %}">
                                <i class="fas fa-newspaper"></i>
                                إدارة الأخبار
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if active_page == 'admin_management' %}active{% endif %}" href="{% url 'admin_management' %}">
                                <i class="fas fa-user-shield"></i>
                                إدارة المشرفين
                            </a>
                        </li>
                        <li class="nav-item mt-5 d-md-block d-none">
                            <a class="nav-link" href="{% url 'admin_logout' %}">
                                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 main-content">
                <!-- Add messages display here -->
                {% if messages %}
                <div class="container-fluid mb-4">
                    {% for message in messages %}
                    <div class="alert alert-{% if message.tags == 'error' %}danger{% else %}{{ message.tags }}{% endif %} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Theme Toggle Button -->
    <button class="theme-toggle" aria-label="Toggle theme">
        <i class="fas fa-moon"></i>
    </button>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <!-- Mobile Sidebar Toggle Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile sidebar toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            const closeSidebar = document.getElementById('closeSidebar');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.createElement('div');

            overlay.classList.add('sidebar-overlay');
            overlay.style.position = 'fixed';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
            overlay.style.zIndex = '1025';
            overlay.style.display = 'none';
            document.body.appendChild(overlay);

            function showSidebar() {
                sidebar.classList.add('show');
                overlay.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }

            function hideSidebar() {
                sidebar.classList.remove('show');
                overlay.style.display = 'none';
                document.body.style.overflow = '';
            }

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', showSidebar);
            }

            if (closeSidebar) {
                closeSidebar.addEventListener('click', hideSidebar);
            }

            overlay.addEventListener('click', hideSidebar);

            // Close sidebar when clicking on a nav link (on mobile)
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth < 768) {
                        hideSidebar();
                    }
                });
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 768) {
                    hideSidebar();
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>

</html>
