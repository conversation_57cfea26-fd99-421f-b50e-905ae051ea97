from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
import uuid


class AdminProfile(models.Model):
    """Model for admin users with role-based permissions"""
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        verbose_name="المستخدم",
        help_text="حساب المستخدم المرتبط بالمشرف"
    )

    # Full permissions (superuser equivalent)
    full_permissions = models.BooleanField(
        default=False,
        verbose_name="صلاحيات كاملة",
        help_text="منح جميع الصلاحيات للمشرف"
    )

    # Granular permissions
    can_manage_halls = models.BooleanField(
        default=False,
        verbose_name="إدارة القاعات",
        help_text="إنشاء وتعديل وحذف القاعات"
    )
    can_manage_users = models.BooleanField(
        default=False,
        verbose_name="إدارة المستخدمين",
        help_text="عرض وتعديل بيانات المستخدمين"
    )
    can_approve_reservations = models.BooleanField(
        default=False,
        verbose_name="الموافقة على الحجوزات",
        help_text="الموافقة أو رفض طلبات حجز القاعات"
    )
    can_manage_transactions = models.BooleanField(
        default=False,
        verbose_name="إدارة المعاملات",
        help_text="معالجة المعاملات والمدفوعات"
    )
    can_view_reports = models.BooleanField(
        default=False,
        verbose_name="عرض التقارير",
        help_text="الوصول إلى التقارير والإحصائيات"
    )
    can_manage_admins = models.BooleanField(
        default=False,
        verbose_name="إدارة المشرفين",
        help_text="إنشاء وتعديل وحذف حسابات المشرفين"
    )
    can_manage_news = models.BooleanField(
        default=False,
        verbose_name="إدارة الأخبار",
        help_text="إنشاء وتعديل ونشر الأخبار"
    )
    can_handle_complaints = models.BooleanField(
        default=False,
        verbose_name="معالجة الشكاوي",
        help_text="عرض ومعالجة الشكاوي والاقتراحات"
    )

    # Profile information
    phone_number = models.CharField(
        max_length=15,
        blank=True,
        null=True,
        verbose_name="رقم الهاتف"
    )
    department = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name="القسم"
    )

    # Session and security
    last_login_ip = models.GenericIPAddressField(
        blank=True,
        null=True,
        verbose_name="آخر عنوان IP"
    )
    failed_login_attempts = models.PositiveIntegerField(
        default=0,
        verbose_name="محاولات تسجيل الدخول الفاشلة"
    )
    account_locked_until = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name="مقفل حتى"
    )

    # Two-factor authentication
    two_factor_enabled = models.BooleanField(
        default=False,
        verbose_name="المصادقة الثنائية مفعلة"
    )
    two_factor_secret = models.CharField(
        max_length=32,
        blank=True,
        null=True,
        verbose_name="مفتاح المصادقة الثنائية"
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_admins',
        verbose_name="أنشئ بواسطة"
    )

    class Meta:
        verbose_name = "ملف المشرف"
        verbose_name_plural = "ملفات المشرفين"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username} - {self.get_role_display()}"

    def get_role_display(self):
        """Get a human-readable role description"""
        if self.full_permissions:
            return "مشرف عام"

        roles = []
        if self.can_manage_halls:
            roles.append("إدارة القاعات")
        if self.can_manage_users:
            roles.append("إدارة المستخدمين")
        if self.can_approve_reservations:
            roles.append("الموافقة على الحجوزات")
        if self.can_manage_transactions:
            roles.append("إدارة المعاملات")
        if self.can_view_reports:
            roles.append("التقارير")
        if self.can_manage_admins:
            roles.append("إدارة المشرفين")
        if self.can_manage_news:
            roles.append("إدارة الأخبار")
        if self.can_handle_complaints:
            roles.append("معالجة الشكاوي")

        return " | ".join(roles) if roles else "بدون صلاحيات"

    def has_permission(self, permission):
        """Check if admin has a specific permission"""
        if self.full_permissions:
            return True

        permission_map = {
            'manage_halls': self.can_manage_halls,
            'manage_users': self.can_manage_users,
            'approve_reservations': self.can_approve_reservations,
            'manage_transactions': self.can_manage_transactions,
            'view_reports': self.can_view_reports,
            'manage_admins': self.can_manage_admins,
            'manage_news': self.can_manage_news,
            'handle_complaints': self.can_handle_complaints,
        }

        return permission_map.get(permission, False)

    def is_account_locked(self):
        """Check if account is currently locked"""
        if self.account_locked_until:
            return timezone.now() < self.account_locked_until
        return False

    def lock_account(self, duration_minutes=30):
        """Lock account for specified duration"""
        self.account_locked_until = timezone.now() + timezone.timedelta(minutes=duration_minutes)
        self.save()

    def unlock_account(self):
        """Unlock account and reset failed attempts"""
        self.account_locked_until = None
        self.failed_login_attempts = 0
        self.save()

    def increment_failed_login(self):
        """Increment failed login attempts and lock if threshold reached"""
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= 5:  # Lock after 5 failed attempts
            self.lock_account()
        self.save()

    def reset_failed_login(self):
        """Reset failed login attempts on successful login"""
        self.failed_login_attempts = 0
        self.save()


class Person(models.Model):
    """Model for citizens/residents data"""
    national_id = models.CharField(
        max_length=20,
        unique=True,
        primary_key=True,
        verbose_name="رقم الهوية الوطنية",
        help_text="رقم الهوية الوطنية للمواطن"
    )
    name = models.CharField(
        max_length=200,
        verbose_name="الاسم الكامل",
        help_text="الاسم الثلاثي للمواطن"
    )
    debts_amount_2024 = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        verbose_name="المديونية لعام 2024",
        help_text="المبلغ المستحق على المواطن بالشيقل"
    )

    # Firebase sync fields
    firebase_doc_id = models.CharField(max_length=50, blank=True, null=True)
    last_synced = models.DateTimeField(null=True, blank=True)
    sync_status = models.CharField(
        max_length=20,
        choices=[
            ('synced', 'متزامن'),
            ('pending', 'في الانتظار'),
            ('error', 'خطأ في المزامنة')
        ],
        default='pending'
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "مواطن"
        verbose_name_plural = "المواطنون"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.national_id})"

    @property
    def has_debts(self):
        """Check if person has outstanding debts"""
        return self.debts_amount_2024 > 240

    @property
    def can_make_transactions(self):
        """Check if person can make new transactions (debt <= 240)"""
        return self.debts_amount_2024 <= 240

    def add_debt(self, amount, description=""):
        """Add debt to person's account"""
        self.debts_amount_2024 += amount
        self.save()
        return self.debts_amount_2024

    def pay_debt(self, amount, description=""):
        """Pay off debt from person's account"""
        self.debts_amount_2024 -= amount
        self.save()
        return self.debts_amount_2024

    def get_active_reservations(self):
        """Get all active (non-cancelled, non-expired) reservations"""
        return self.reservations.filter(
            reservation_status__in=['in_progress', 'confirmed']
        ).order_by('-created_at')

    def get_pending_transactions(self):
        """Get all pending transactions"""
        return self.transactions.filter(
            transaction_status='in_progress'
        ).order_by('-created_at')

    def can_reserve_hall(self):
        """Check if person can make a new hall reservation (legacy method)"""
        # Check if person has any active reservations
        active_reservations = self.get_active_reservations()
        return not active_reservations.exists()

    def can_user_make_reservation(self):
        """
        Check if a regular user can make a new reservation based on new policy:
        - User can make a new reservation only if they don't have a pending reservation
        - Or they only have confirmed reservations
        """
        # Check for pending reservations (in_progress status)
        pending_reservations = self.reservations.filter(reservation_status='in_progress')

        if pending_reservations.exists():
            return False, "You already have a pending reservation. Please confirm or cancel it before reserving again."

        return True, ""

    def can_admin_reserve_for_user(self):
        """
        Check if an admin can reserve on behalf of this user based on new policy:
        - Admin can reserve for a user only if that user has at least one confirmed reservation
        """
        confirmed_reservations = self.reservations.filter(reservation_status='confirmed')

        if not confirmed_reservations.exists():
            return False, "This user must have at least one confirmed reservation before an admin can reserve on their behalf."

        return True, ""

    def get_reservation_status_summary(self):
        """Get a summary of user's reservation statuses"""
        reservations = self.reservations.all()

        summary = {
            'total': reservations.count(),
            'confirmed': reservations.filter(reservation_status='confirmed').count(),
            'pending': reservations.filter(reservation_status='in_progress').count(),
            'cancelled': reservations.filter(reservation_status='cancelled').count(),
            'expired': reservations.filter(reservation_status='expired').count(),
        }

        return summary

    def can_make_transaction_type(self, transaction_type):
        """Check if person can make a specific type of transaction"""
        if not self.can_make_transactions:
            return False, "المواطن عليه ديون تزيد عن 240 شيقل"

        # Check if person already has this transaction type
        existing = self.transactions.filter(
            transaction_type=transaction_type,
            transaction_status__in=['in_progress', 'done']
        ).exists()

        if existing:
            return False, "المواطن لديه طلب من هذا النوع بالفعل"

        return True, "يمكن تقديم الطلب"


class HallReservation(models.Model):
    """Model for hall reservation requests"""
    RESERVATION_STATUS_CHOICES = [
        ('in_progress', 'قيد التنفيذ'),
        ('confirmed', 'مؤكد'),
        ('cancelled', 'ملغي'),
        ('expired', 'منتهي الصلاحية')
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    person = models.ForeignKey(
        Person,
        on_delete=models.CASCADE,
        to_field='national_id',
        verbose_name="المواطن",
        related_name='reservations'
    )
    full_name = models.CharField(
        max_length=200,
        verbose_name="الاسم الكامل"
    )
    phone_number = models.CharField(
        max_length=15,
        verbose_name="رقم الهاتف"
    )
    event_type = models.CharField(
        max_length=100,
        verbose_name="نوع المناسبة"
    )
    start_date = models.DateField(verbose_name="تاريخ البداية")
    end_date = models.DateField(verbose_name="تاريخ النهاية")
    reservation_status = models.CharField(
        max_length=20,
        choices=RESERVATION_STATUS_CHOICES,
        default='in_progress',
        verbose_name="حالة الحجز"
    )
    admin_created = models.BooleanField(
        default=False,
        verbose_name="تم إنشاؤه من قبل الإدارة"
    )

    # Firebase sync fields
    firebase_doc_id = models.CharField(max_length=100, blank=True, null=True)
    last_synced = models.DateTimeField(null=True, blank=True)
    sync_status = models.CharField(
        max_length=20,
        choices=[
            ('synced', 'متزامن'),
            ('pending', 'في الانتظار'),
            ('error', 'خطأ في المزامنة')
        ],
        default='pending'
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "حجز قاعة"
        verbose_name_plural = "حجوزات القاعات"
        ordering = ['-created_at']

    def __str__(self):
        return f"حجز {self.person.name} - {self.event_type}"

    @property
    def days_remaining(self):
        """Calculate days remaining before expiration (3 days for unconfirmed)"""
        if self.reservation_status == 'confirmed':
            return 0
        days_passed = (timezone.now().date() - self.created_at.date()).days
        return max(0, 3 - days_passed)

    @property
    def is_expired(self):
        """Check if reservation has expired (more than 3 days and not confirmed)"""
        return self.days_remaining == 0 and self.reservation_status != 'confirmed'

    def confirm_reservation(self):
        """Confirm the reservation"""
        self.reservation_status = 'confirmed'
        self.save()
        return True

    def cancel_reservation(self, reason=""):
        """Cancel the reservation"""
        self.reservation_status = 'cancelled'
        self.save()
        return True

    def check_date_conflicts(self):
        """Check if reservation dates conflict with existing confirmed reservations"""
        conflicts = HallReservation.objects.filter(
            reservation_status='confirmed'
        ).exclude(id=self.id).filter(
            start_date__lte=self.end_date,
            end_date__gte=self.start_date
        )
        return conflicts.exists()

    def auto_expire_if_needed(self):
        """Auto-expire reservation if it's been more than 3 days and not confirmed"""
        if self.is_expired and self.reservation_status == 'in_progress':
            self.reservation_status = 'expired'
            self.save()
            return True
        return False

    @classmethod
    def cleanup_expired_reservations(cls):
        """Class method to cleanup all expired reservations"""
        expired_count = 0
        for reservation in cls.objects.filter(reservation_status='in_progress'):
            if reservation.auto_expire_if_needed():
                expired_count += 1
        return expired_count

    def save(self, *args, **kwargs):
        """Override save to validate dates and check conflicts"""
        # Validate dates
        if self.start_date and self.end_date:
            if self.start_date > self.end_date:
                raise ValueError("تاريخ البداية يجب أن يكون قبل تاريخ النهاية")

            # Check for past dates (only for new reservations)
            if not self.pk:  # New reservation
                from django.utils import timezone
                today = timezone.now().date()
                if self.start_date < today:
                    raise ValueError("لا يمكن حجز تواريخ في الماضي")

        super().save(*args, **kwargs)

    @classmethod
    def validate_user_reservation(cls, person, is_admin_created=False):
        """
        Validate if a reservation can be created based on new policy rules

        Args:
            person: Person object for whom the reservation is being made
            is_admin_created: Boolean indicating if this is an admin-created reservation

        Returns:
            tuple: (can_reserve: bool, error_message: str)
        """
        if is_admin_created:
            # Admin reservation validation
            return person.can_admin_reserve_for_user()
        else:
            # Regular user reservation validation
            return person.can_user_make_reservation()

    @classmethod
    def create_reservation_with_validation(cls, person, full_name, phone_number, event_type,
                                         start_date, end_date, is_admin_created=False, **kwargs):
        """
        Create a reservation with policy validation

        Args:
            person: Person object
            full_name: Full name for the reservation
            phone_number: Phone number
            event_type: Type of event
            start_date: Start date
            end_date: End date
            is_admin_created: Boolean indicating if admin created
            **kwargs: Additional fields

        Returns:
            tuple: (reservation: HallReservation or None, success: bool, message: str)
        """
        # Validate reservation policy
        can_reserve, error_message = cls.validate_user_reservation(person, is_admin_created)

        if not can_reserve:
            return None, False, error_message

        try:
            # Create the reservation
            reservation = cls.objects.create(
                person=person,
                full_name=full_name,
                phone_number=phone_number,
                event_type=event_type,
                start_date=start_date,
                end_date=end_date,
                admin_created=is_admin_created,
                **kwargs
            )

            return reservation, True, "Reservation created successfully."

        except Exception as e:
            return None, False, f"Error creating reservation: {str(e)}"


class Transaction(models.Model):
    """Model for citizen service transactions/requests"""
    TRANSACTION_STATUS_CHOICES = [
        ('in_progress', 'قيد التنفيذ'),
        ('done', 'مكتمل'),
        ('cancelled', 'ملغي')
    ]

    TRANSACTION_TYPE_CHOICES = [
        ('route_marking', 'طلب تعليم طريق'),
        ('site_plan_guide', 'طلب دليل مخطط موقع'),
        ('building_permit', 'طلب ترخيص مبنى'),
        ('water_line_noc', 'طلب عدم ممانعة لتقديم خدمة خط مياه'),
        ('electricity_line_noc', 'طلب عدم ممانعة لمد خط كهرباء'),
        ('proof_of_residence', 'طلب إثبات سكن'),
        ('employment_proof', 'طلب إثبات عمل'),
        ('sale_transaction_palestinian', 'طلب معاملات صفقة بيع (لحملة الهوية الفلسطينية) وصف قطعة، براءة ذمة'),
        ('sale_transaction_jerusalem', 'طلب معاملات صفقة بيع (لحملة هوية القدس) وصف قطعة، براءة ذمة وتصنيف'),
        ('clearance_kushan', 'طلب معاملة براءة ذمة لاستخراج الكوشان'),
        ('other', 'طلب معاملة أخرى او استفسار')
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    person = models.ForeignKey(
        Person,
        on_delete=models.CASCADE,
        to_field='national_id',
        verbose_name="المواطن",
        related_name='transactions'
    )
    full_name = models.CharField(
        max_length=200,
        verbose_name="الاسم الكامل"
    )
    phone_number = models.CharField(
        max_length=15,
        verbose_name="رقم الهاتف"
    )
    transaction_type = models.CharField(
        max_length=50,
        choices=TRANSACTION_TYPE_CHOICES,
        verbose_name="نوع الطلب"
    )
    additional_notes = models.TextField(
        blank=True,
        default="لا يوجد ملاحظات إضافية",
        verbose_name="ملاحظات إضافية"
    )
    transaction_status = models.CharField(
        max_length=20,
        choices=TRANSACTION_STATUS_CHOICES,
        default='in_progress',
        verbose_name="حالة الطلب"
    )
    receipt_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name="رقم الإيصال"
    )
    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="تاريخ الإكمال"
    )

    # Firebase sync fields
    firebase_doc_id = models.CharField(max_length=100, blank=True, null=True)
    last_synced = models.DateTimeField(null=True, blank=True)
    sync_status = models.CharField(
        max_length=20,
        choices=[
            ('synced', 'متزامن'),
            ('pending', 'في الانتظار'),
            ('error', 'خطأ في المزامنة')
        ],
        default='pending'
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "طلب معاملة"
        verbose_name_plural = "طلبات المعاملات"
        ordering = ['-created_at']
        # Ensure one transaction type per person
        unique_together = ['person', 'transaction_type']

    def __str__(self):
        return f"{self.person.name} - {self.get_transaction_type_display()}"

    def save(self, *args, **kwargs):
        """Auto-set completed_at when status changes to done"""
        if self.transaction_status == 'done' and not self.completed_at:
            self.completed_at = timezone.now()
        super().save(*args, **kwargs)

    def mark_as_done(self, receipt_number=None):
        """Mark transaction as completed"""
        self.transaction_status = 'done'
        self.completed_at = timezone.now()
        if receipt_number:
            self.receipt_number = receipt_number
        self.save()
        return True

    def mark_as_cancelled(self, reason=""):
        """Mark transaction as cancelled"""
        self.transaction_status = 'cancelled'
        self.save()
        return True

    def can_be_deleted(self):
        """Check if transaction can be safely deleted"""
        # Completed transactions with receipt numbers should not be deleted
        if self.transaction_status == 'done' and self.receipt_number:
            return False, "لا يمكن حذف الطلب المكتمل الذي له رقم إيصال"
        return True, "يمكن حذف الطلب"

    @property
    def processing_time(self):
        """Calculate processing time for completed transactions"""
        if self.completed_at and self.created_at:
            return self.completed_at - self.created_at
        return None

    @classmethod
    def get_statistics(cls, year=None):
        """Get transaction statistics"""
        from django.db.models import Count, Q
        from django.utils import timezone

        if not year:
            year = timezone.now().year

        queryset = cls.objects.filter(created_at__year=year)

        stats = {
            'total': queryset.count(),
            'completed': queryset.filter(transaction_status='done').count(),
            'in_progress': queryset.filter(transaction_status='in_progress').count(),
            'cancelled': queryset.filter(transaction_status='cancelled').count(),
            'by_type': queryset.values('transaction_type').annotate(count=Count('id')),
        }

        # Calculate completion rate
        if stats['total'] > 0:
            stats['completion_rate'] = (stats['completed'] / stats['total']) * 100
        else:
            stats['completion_rate'] = 0

        return stats


class SuggestionComplaint(models.Model):
    """Model for citizen suggestions and complaints"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    person = models.ForeignKey(
        Person,
        on_delete=models.CASCADE,
        to_field='national_id',
        verbose_name="المواطن",
        related_name='suggestions'
    )
    full_name = models.CharField(
        max_length=200,
        verbose_name="الاسم الكامل",
        default="بدون إسم"
    )
    message = models.TextField(verbose_name="الرسالة")

    # Firebase sync fields
    firebase_doc_id = models.CharField(max_length=100, blank=True, null=True)
    last_synced = models.DateTimeField(null=True, blank=True)
    sync_status = models.CharField(
        max_length=20,
        choices=[
            ('synced', 'متزامن'),
            ('pending', 'في الانتظار'),
            ('error', 'خطأ في المزامنة')
        ],
        default='pending'
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "اقتراح أو شكوى"
        verbose_name_plural = "الاقتراحات والشكاوى"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.person.name} - {self.message[:50]}..."


class NewsAchievement(models.Model):
    """Model for news and achievements"""
    NEWS_TYPE_CHOICES = [
        ('news', 'أخبار'),
        ('achievements', 'إنجازات'),
        ('projects', 'مشاريع'),
        ('announcements', 'إعلانات')
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(
        max_length=255,
        verbose_name="عنوان الخبر"
    )
    description = models.TextField(verbose_name="وصف الخبر")

    # Image fields - supporting both Firebase and local storage
    image_src = models.JSONField(
        blank=True,
        null=True,
        verbose_name="الصورة الرئيسية",
        help_text="JSON containing firebase_url and/or local_url"
    )
    additional_images = models.JSONField(
        blank=True,
        null=True,
        verbose_name="صور إضافية",
        help_text="Array of image URLs"
    )

    video_url = models.URLField(
        blank=True,
        null=True,
        verbose_name="رابط الفيديو"
    )
    news_type = models.CharField(
        max_length=50,
        choices=NEWS_TYPE_CHOICES,
        default='news',
        verbose_name="نوع الخبر"
    )
    is_featured = models.BooleanField(
        default=False,
        verbose_name="خبر مميز",
        help_text="سيظهر كخبر رئيسي في الصفحة الرئيسية"
    )
    is_approved = models.BooleanField(
        default=False,
        verbose_name="معتمد للنشر"
    )
    published_at = models.DateField(
        null=True,
        blank=True,
        verbose_name="تاريخ النشر"
    )

    # Firebase sync fields
    firebase_doc_id = models.CharField(max_length=100, blank=True, null=True)
    last_synced = models.DateTimeField(null=True, blank=True)
    sync_status = models.CharField(
        max_length=20,
        choices=[
            ('synced', 'متزامن'),
            ('pending', 'في الانتظار'),
            ('error', 'خطأ في المزامنة')
        ],
        default='pending'
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="أنشئ بواسطة"
    )

    class Meta:
        verbose_name = "خبر أو إنجاز"
        verbose_name_plural = "الأخبار والإنجازات"
        ordering = ['-published_at', '-created_at']

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        """Ensure only one featured news at a time"""
        if self.is_featured:
            # Unfeature all other news
            NewsAchievement.objects.filter(is_featured=True).update(is_featured=False)
        super().save(*args, **kwargs)


class SystemSettings(models.Model):
    """Model for system-wide settings"""
    key = models.CharField(
        max_length=100,
        unique=True,
        verbose_name="المفتاح"
    )
    value = models.TextField(verbose_name="القيمة")
    description = models.TextField(
        blank=True,
        verbose_name="الوصف"
    )

    # Firebase sync fields
    firebase_doc_id = models.CharField(max_length=100, blank=True, null=True)
    last_synced = models.DateTimeField(null=True, blank=True)
    sync_status = models.CharField(
        max_length=20,
        choices=[
            ('synced', 'متزامن'),
            ('pending', 'في الانتظار'),
            ('error', 'خطأ في المزامنة')
        ],
        default='pending'
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "إعداد النظام"
        verbose_name_plural = "إعدادات النظام"
        ordering = ['key']

    def __str__(self):
        return f"{self.key}: {self.value[:50]}..."


class FirebaseSyncLog(models.Model):
    """Model to track Firebase synchronization operations"""
    SYNC_TYPE_CHOICES = [
        ('import', 'استيراد من Firebase'),
        ('export', 'تصدير إلى Firebase'),
        ('bidirectional', 'مزامنة ثنائية الاتجاه')
    ]

    SYNC_STATUS_CHOICES = [
        ('started', 'بدأت'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتملة'),
        ('failed', 'فشلت'),
        ('partial', 'مكتملة جزئياً')
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    collection_name = models.CharField(
        max_length=100,
        verbose_name="اسم المجموعة"
    )
    sync_type = models.CharField(
        max_length=20,
        choices=SYNC_TYPE_CHOICES,
        verbose_name="نوع المزامنة"
    )
    sync_status = models.CharField(
        max_length=20,
        choices=SYNC_STATUS_CHOICES,
        default='started',
        verbose_name="حالة المزامنة"
    )
    records_processed = models.PositiveIntegerField(
        default=0,
        verbose_name="عدد السجلات المعالجة"
    )
    records_successful = models.PositiveIntegerField(
        default=0,
        verbose_name="عدد السجلات الناجحة"
    )
    records_failed = models.PositiveIntegerField(
        default=0,
        verbose_name="عدد السجلات الفاشلة"
    )
    error_message = models.TextField(
        blank=True,
        null=True,
        verbose_name="رسالة الخطأ"
    )
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    initiated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="بدأ بواسطة"
    )

    class Meta:
        verbose_name = "سجل مزامنة Firebase"
        verbose_name_plural = "سجلات مزامنة Firebase"
        ordering = ['-started_at']

    def __str__(self):
        return f"{self.collection_name} - {self.get_sync_type_display()} - {self.get_sync_status_display()}"

    @property
    def duration(self):
        """Calculate sync duration"""
        if self.completed_at:
            return self.completed_at - self.started_at
        return None

    @property
    def success_rate(self):
        """Calculate success rate percentage"""
        if self.records_processed > 0:
            return (self.records_successful / self.records_processed) * 100
        return 0