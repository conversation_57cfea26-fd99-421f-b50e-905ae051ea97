from django.shortcuts import redirect
from django.contrib.auth.decorators import user_passes_test
from django.urls import reverse
from django.http import HttpResponseRedirect

class AdminLoginRequiredMiddleware:
    """
    Middleware to handle both legacy custom admin and new Django admin authentication
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Handle legacy custom admin paths (keep for backward compatibility)
        if request.path.startswith('/admin/') and not request.path.startswith('/admin/login/'):
            # Check if it's a Django admin path
            if self._is_django_admin_path(request.path):
                # Let Django admin handle its own authentication
                pass
            else:
                # Legacy custom admin - check session authentication
                if not request.session.get('is_admin_authenticated'):
                    return redirect('admin_login')

        # Handle legacy admin_login path - redirect to Django admin
        if request.path == '/admin_login' or request.path == '/admin_login/':
            return HttpResponseRedirect('/admin/')

        response = self.get_response(request)
        return response

    def _is_django_admin_path(self, path):
        """Check if the path is a Django admin path"""
        django_admin_patterns = [
            '/admin/login/',
            '/admin/logout/',
            '/admin/password_change/',
            '/admin/password_change/done/',
            '/admin/jsi18n/',
            '/admin/KLC_App/',  # Our app's admin URLs
            '/admin/auth/',     # User management
            '/admin/contenttypes/',
            '/admin/sessions/',
        ]

        # Check if path starts with any Django admin pattern
        for pattern in django_admin_patterns:
            if path.startswith(pattern):
                return True

        # Check if it's the main admin index
        if path == '/admin/' or path == '/admin':
            return True

        return False


class LegacyAdminRedirectMiddleware:
    """
    Middleware to redirect legacy admin URLs to Django admin equivalents
    """
    def __init__(self, get_response):
        self.get_response = get_response

        # Mapping of legacy URLs to Django admin URLs
        self.url_mappings = {
            '/admin/users/': '/admin/KLC_App/person/',
            '/admin/reservations/': '/admin/KLC_App/hallreservation/',
            '/admin/transactions/': '/admin/KLC_App/transaction/',
            '/admin/suggestions/': '/admin/KLC_App/suggestioncomplaint/',
            '/admin/news/': '/admin/KLC_App/newsachievement/',
            '/admin/add-user/': '/admin/KLC_App/person/add/',
            '/admin/add-news/': '/admin/KLC_App/newsachievement/add/',
            '/admin/add-reservation/': '/admin/KLC_App/hallreservation/add/',
            '/admin/add-transaction/': '/admin/KLC_App/transaction/add/',
        }

    def __call__(self, request):
        # Check if the current path should be redirected
        if request.path in self.url_mappings:
            return HttpResponseRedirect(self.url_mappings[request.path])

        response = self.get_response(request)
        return response


class CustomAdminPanelMiddleware:
    """
    Middleware for our custom admin panel with Django-based authentication
    """
    def __init__(self, get_response):
        self.get_response = get_response

        # Custom admin panel URLs that require authentication
        self.admin_panel_urls = [
            '/admin-panel/',
        ]

        # URLs that don't require authentication
        self.exempt_urls = [
            '/admin-panel/login/',
            '/admin-panel/logout/',
        ]

    def __call__(self, request):
        # Check if the request is for our custom admin panel
        is_admin_panel_url = any(request.path.startswith(url) for url in self.admin_panel_urls)
        is_exempt = any(request.path.startswith(url) for url in self.exempt_urls)

        if is_admin_panel_url and not is_exempt:
            # Check Django authentication
            if not request.user.is_authenticated:
                return redirect('/admin-panel/login/')

            # Check if user has admin profile
            try:
                from .models import AdminProfile
                admin_profile = AdminProfile.objects.get(user=request.user)

                # Check if account is locked
                if admin_profile.is_account_locked():
                    from django.contrib.auth import logout
                    from django.contrib import messages
                    logout(request)
                    messages.error(request, 'حسابك مقفل مؤقتاً. يرجى المحاولة لاحقاً.')
                    return redirect('/admin-panel/login/')

            except AdminProfile.DoesNotExist:
                from django.contrib.auth import logout
                from django.contrib import messages
                logout(request)
                messages.error(request, 'ليس لديك صلاحيات إدارية.')
                return redirect('/admin-panel/login/')

        response = self.get_response(request)
        return response
