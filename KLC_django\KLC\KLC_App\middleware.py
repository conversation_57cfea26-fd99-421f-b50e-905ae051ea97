from django.shortcuts import redirect
from django.contrib.auth.decorators import user_passes_test
from django.urls import reverse
from django.http import HttpResponseRedirect

class AdminLoginRequiredMiddleware:
    """
    Middleware to handle both legacy custom admin and new Django admin authentication
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Handle legacy custom admin paths (keep for backward compatibility)
        if request.path.startswith('/admin/') and not request.path.startswith('/admin/login/'):
            # Check if it's a Django admin path
            if self._is_django_admin_path(request.path):
                # Let Django admin handle its own authentication
                pass
            else:
                # Legacy custom admin - check session authentication
                if not request.session.get('is_admin_authenticated'):
                    return redirect('admin_login')

        # Handle legacy admin_login path - redirect to Django admin
        if request.path == '/admin_login' or request.path == '/admin_login/':
            return HttpResponseRedirect('/admin/')

        response = self.get_response(request)
        return response

    def _is_django_admin_path(self, path):
        """Check if the path is a Django admin path"""
        django_admin_patterns = [
            '/admin/login/',
            '/admin/logout/',
            '/admin/password_change/',
            '/admin/password_change/done/',
            '/admin/jsi18n/',
            '/admin/KLC_App/',  # Our app's admin URLs
            '/admin/auth/',     # User management
            '/admin/contenttypes/',
            '/admin/sessions/',
        ]

        # Check if path starts with any Django admin pattern
        for pattern in django_admin_patterns:
            if path.startswith(pattern):
                return True

        # Check if it's the main admin index
        if path == '/admin/' or path == '/admin':
            return True

        return False


class LegacyAdminRedirectMiddleware:
    """
    Middleware to redirect legacy admin URLs to Django admin equivalents
    """
    def __init__(self, get_response):
        self.get_response = get_response

        # Mapping of legacy URLs to Django admin URLs
        self.url_mappings = {
            '/admin/users/': '/admin/KLC_App/person/',
            '/admin/reservations/': '/admin/KLC_App/hallreservation/',
            '/admin/transactions/': '/admin/KLC_App/transaction/',
            '/admin/suggestions/': '/admin/KLC_App/suggestioncomplaint/',
            '/admin/news/': '/admin/KLC_App/newsachievement/',
            '/admin/add-user/': '/admin/KLC_App/person/add/',
            '/admin/add-news/': '/admin/KLC_App/newsachievement/add/',
            '/admin/add-reservation/': '/admin/KLC_App/hallreservation/add/',
            '/admin/add-transaction/': '/admin/KLC_App/transaction/add/',
        }

    def __call__(self, request):
        # Check if the current path should be redirected
        if request.path in self.url_mappings:
            return HttpResponseRedirect(self.url_mappings[request.path])

        response = self.get_response(request)
        return response
