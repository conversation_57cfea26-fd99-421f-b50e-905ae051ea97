"""
Firebase-SQLite Sync Service

This module provides functionality to synchronize data between Firebase Firestore
and local SQLite database models. It supports bidirectional sync with conflict
resolution and caching strategies.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from django.conf import settings
from django.utils import timezone
from django.db import transaction
from django.contrib.auth.models import User
from .models import (
    Person, HallReservation, Transaction, SuggestionComplaint, 
    NewsAchievement, SystemSettings, FirebaseSyncLog
)

logger = logging.getLogger(__name__)


class FirebaseSyncService:
    """Service class for Firebase-SQLite synchronization"""
    
    def __init__(self):
        self.db = settings.DB
        self.collection_model_mapping = {
            'persons': Person,
            'hall_reservations': HallReservation,
            'transactions': Transaction,
            'suggestions_complaints': SuggestionComplaint,
            'news_achievements': NewsAchievement,
            'system_settings': SystemSettings,
        }
    
    def sync_collection_from_firebase(self, collection_name: str, user: Optional[User] = None) -> FirebaseSyncLog:
        """
        Sync a specific collection from Firebase to SQLite
        
        Args:
            collection_name: Name of the Firebase collection
            user: User who initiated the sync
            
        Returns:
            FirebaseSyncLog: Log of the sync operation
        """
        sync_log = FirebaseSyncLog.objects.create(
            collection_name=collection_name,
            sync_type='import',
            initiated_by=user
        )
        
        try:
            sync_log.sync_status = 'in_progress'
            sync_log.save()
            
            model_class = self.collection_model_mapping.get(collection_name)
            if not model_class:
                raise ValueError(f"No model mapping found for collection: {collection_name}")
            
            # Get all documents from Firebase collection
            firebase_docs = list(self.db.collection(collection_name).stream())
            sync_log.records_processed = len(firebase_docs)
            sync_log.save()
            
            successful_count = 0
            failed_count = 0
            
            with transaction.atomic():
                for doc in firebase_docs:
                    try:
                        self._sync_document_to_model(doc, model_class, collection_name)
                        successful_count += 1
                    except Exception as e:
                        logger.error(f"Failed to sync document {doc.id}: {str(e)}")
                        failed_count += 1
            
            sync_log.records_successful = successful_count
            sync_log.records_failed = failed_count
            sync_log.sync_status = 'completed' if failed_count == 0 else 'partial'
            sync_log.completed_at = timezone.now()
            sync_log.save()
            
            logger.info(f"Sync completed for {collection_name}: {successful_count} successful, {failed_count} failed")
            
        except Exception as e:
            sync_log.sync_status = 'failed'
            sync_log.error_message = str(e)
            sync_log.completed_at = timezone.now()
            sync_log.save()
            logger.error(f"Sync failed for {collection_name}: {str(e)}")
            raise
        
        return sync_log
    
    def _sync_document_to_model(self, doc, model_class, collection_name: str):
        """
        Sync a single Firebase document to Django model
        
        Args:
            doc: Firebase document
            model_class: Django model class
            collection_name: Name of the collection
        """
        doc_data = doc.to_dict()
        doc_id = doc.id
        
        # Handle different collection types
        if collection_name == 'persons':
            self._sync_person_document(doc_data, doc_id)
        elif collection_name == 'hall_reservations':
            self._sync_reservation_document(doc_data, doc_id)
        elif collection_name == 'transactions':
            self._sync_transaction_document(doc_data, doc_id)
        elif collection_name == 'suggestions_complaints':
            self._sync_suggestion_document(doc_data, doc_id)
        elif collection_name == 'news_achievements':
            self._sync_news_document(doc_data, doc_id)
        elif collection_name == 'system_settings':
            self._sync_settings_document(doc_data, doc_id)
    
    def _sync_person_document(self, doc_data: Dict, doc_id: str):
        """Sync person document from Firebase"""
        national_id = doc_data.get('national_id', doc_id)
        
        person, created = Person.objects.update_or_create(
            national_id=national_id,
            defaults={
                'name': doc_data.get('name', ''),
                'debts_amount_2024': float(doc_data.get('debts_amount_2024', 0)),
                'firebase_doc_id': doc_id,
                'last_synced': timezone.now(),
                'sync_status': 'synced'
            }
        )
        return person
    
    def _sync_reservation_document(self, doc_data: Dict, doc_id: str):
        """Sync hall reservation document from Firebase"""
        id_number = doc_data.get('id_number')
        if not id_number:
            raise ValueError(f"Missing id_number in reservation document {doc_id}")
        
        # Ensure person exists
        person, _ = Person.objects.get_or_create(
            national_id=id_number,
            defaults={'name': doc_data.get('full_name', ''), 'debts_amount_2024': 0}
        )
        
        # Map status values
        status_mapping = {
            'In Progress': 'in_progress',
            'confirmed': 'confirmed',
            'cancelled': 'cancelled',
            'expired': 'expired'
        }
        
        reservation_status = status_mapping.get(
            doc_data.get('reservation_status', 'In Progress'), 
            'in_progress'
        )
        
        # Parse dates
        start_date = self._parse_date(doc_data.get('start_date'))
        end_date = self._parse_date(doc_data.get('end_date'))
        created_at = self._parse_datetime(doc_data.get('created_at'))
        
        reservation, created = HallReservation.objects.update_or_create(
            firebase_doc_id=doc_id,
            defaults={
                'person': person,
                'full_name': doc_data.get('full_name', ''),
                'phone_number': doc_data.get('phone_number', ''),
                'event_type': doc_data.get('event_type', ''),
                'start_date': start_date,
                'end_date': end_date,
                'reservation_status': reservation_status,
                'admin_created': doc_data.get('admin_created', False),
                'created_at': created_at or timezone.now(),
                'last_synced': timezone.now(),
                'sync_status': 'synced'
            }
        )
        return reservation
    
    def _sync_transaction_document(self, doc_data: Dict, doc_id: str):
        """Sync transaction document from Firebase"""
        id_number = doc_data.get('id_number')
        if not id_number:
            raise ValueError(f"Missing id_number in transaction document {doc_id}")
        
        # Ensure person exists
        person, _ = Person.objects.get_or_create(
            national_id=id_number,
            defaults={'name': doc_data.get('full_name', ''), 'debts_amount_2024': 0}
        )
        
        # Map transaction types from Arabic to English keys
        transaction_type_mapping = {
            'طلب تعليم طريق': 'route_marking',
            'طلب دليل مخطط موقع': 'site_plan_guide',
            'طلب ترخيص مبنى': 'building_permit',
            'طلب عدم ممانعة لتقديم خدمة خط مياه': 'water_line_noc',
            'طلب عدم ممانعة لمد خط كهرباء': 'electricity_line_noc',
            'طلب إثبات سكن': 'proof_of_residence',
            'طلب إثبات عمل': 'employment_proof',
            'طلب معاملات صفقة بيع (لحملة الهوية الفلسطينية) وصف قطعة، براءة ذمة': 'sale_transaction_palestinian',
            'طلب معاملات صفقة بيع (لحملة هوية القدس) وصف قطعة، براءة ذمة وتصنيف': 'sale_transaction_jerusalem',
            'طلب معاملة براءة ذمة لاستخراج الكوشان': 'clearance_kushan',
            'طلب معاملة أخرى او استفسار': 'other'
        }
        
        # Map status values
        status_mapping = {
            'In Progress': 'in_progress',
            'Done': 'done',
            'cancelled': 'cancelled'
        }
        
        transaction_type_ar = doc_data.get('transaction_type', '')
        transaction_type = transaction_type_mapping.get(transaction_type_ar, 'other')
        transaction_status = status_mapping.get(
            doc_data.get('transaction_status', 'In Progress'), 
            'in_progress'
        )
        
        # Parse dates
        created_at = self._parse_datetime(doc_data.get('created_at'))
        completed_at = self._parse_datetime(doc_data.get('completed_at'))
        
        transaction_obj, created = Transaction.objects.update_or_create(
            firebase_doc_id=doc_id,
            defaults={
                'person': person,
                'full_name': doc_data.get('full_name', ''),
                'phone_number': doc_data.get('phone_number', ''),
                'transaction_type': transaction_type,
                'additional_notes': doc_data.get('additional_notes', 'لا يوجد ملاحظات إضافية'),
                'transaction_status': transaction_status,
                'receipt_number': doc_data.get('receipt_number'),
                'completed_at': completed_at,
                'created_at': created_at or timezone.now(),
                'last_synced': timezone.now(),
                'sync_status': 'synced'
            }
        )
        return transaction_obj
    
    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse date string in various formats"""
        if not date_str:
            return None
        
        formats = ['%Y-%m-%d', '%d-%m-%Y', '%m/%d/%Y']
        for fmt in formats:
            try:
                return datetime.strptime(date_str, fmt).date()
            except ValueError:
                continue
        return None
    
    def _parse_datetime(self, datetime_str: str) -> Optional[datetime]:
        """Parse datetime string in various formats"""
        if not datetime_str:
            return None
        
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d at %H:%M:%S',
            '%Y-%m-%d'
        ]
        for fmt in formats:
            try:
                dt = datetime.strptime(datetime_str, fmt)
                return timezone.make_aware(dt) if timezone.is_naive(dt) else dt
            except ValueError:
                continue
        return None

    def _sync_suggestion_document(self, doc_data: Dict, doc_id: str):
        """Sync suggestion/complaint document from Firebase"""
        id_number = doc_data.get('id_number')
        if not id_number:
            raise ValueError(f"Missing id_number in suggestion document {doc_id}")

        # Ensure person exists
        person, _ = Person.objects.get_or_create(
            national_id=id_number,
            defaults={'name': doc_data.get('full_name', ''), 'debts_amount_2024': 0}
        )

        created_at = self._parse_datetime(doc_data.get('created_at'))

        suggestion, created = SuggestionComplaint.objects.update_or_create(
            firebase_doc_id=doc_id,
            defaults={
                'person': person,
                'full_name': doc_data.get('full_name', 'بدون إسم'),
                'message': doc_data.get('message', ''),
                'created_at': created_at or timezone.now(),
                'last_synced': timezone.now(),
                'sync_status': 'synced'
            }
        )
        return suggestion

    def _sync_news_document(self, doc_data: Dict, doc_id: str):
        """Sync news/achievement document from Firebase"""
        # Map news types
        type_mapping = {
            'أخبار': 'news',
            'إنجازات': 'achievements',
            'مشاريع': 'projects',
            'إعلانات': 'announcements'
        }

        news_type = type_mapping.get(doc_data.get('type', 'أخبار'), 'news')
        published_at = self._parse_date(doc_data.get('published_at'))

        # Handle approval status - check both fields
        is_approved = doc_data.get('new_approve', False) or doc_data.get('is_approved', False)

        news, created = NewsAchievement.objects.update_or_create(
            firebase_doc_id=doc_id,
            defaults={
                'title': doc_data.get('title', ''),
                'description': doc_data.get('description', ''),
                'image_src': doc_data.get('image_src'),
                'additional_images': doc_data.get('additional_images', []),
                'video_url': doc_data.get('video_url'),
                'news_type': news_type,
                'is_featured': doc_data.get('is_featured', False),
                'is_approved': is_approved,
                'published_at': published_at,
                'last_synced': timezone.now(),
                'sync_status': 'synced'
            }
        )
        return news

    def _sync_settings_document(self, doc_data: Dict, doc_id: str):
        """Sync system settings document from Firebase"""
        # For system_settings, the doc_id is typically the key
        setting, created = SystemSettings.objects.update_or_create(
            key=doc_id,
            defaults={
                'value': doc_data.get('password', '') if doc_id == 'manager_password' else str(doc_data),
                'description': f"Setting synced from Firebase: {doc_id}",
                'firebase_doc_id': doc_id,
                'last_synced': timezone.now(),
                'sync_status': 'synced'
            }
        )
        return setting

    def sync_model_to_firebase(self, model_instance, user: Optional[User] = None) -> bool:
        """
        Sync a Django model instance to Firebase

        Args:
            model_instance: Django model instance to sync
            user: User who initiated the sync

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            collection_name = self._get_collection_name_for_model(model_instance)
            if not collection_name:
                logger.error(f"No collection mapping found for model: {type(model_instance)}")
                return False

            firebase_data = self._model_to_firebase_data(model_instance)
            doc_id = model_instance.firebase_doc_id or self._generate_firebase_doc_id(model_instance)

            # Update or create document in Firebase
            self.db.collection(collection_name).document(doc_id).set(firebase_data)

            # Update model instance
            model_instance.firebase_doc_id = doc_id
            model_instance.last_synced = timezone.now()
            model_instance.sync_status = 'synced'
            model_instance.save(update_fields=['firebase_doc_id', 'last_synced', 'sync_status'])

            logger.info(f"Successfully synced {type(model_instance).__name__} {model_instance.pk} to Firebase")
            return True

        except Exception as e:
            logger.error(f"Failed to sync {type(model_instance).__name__} {model_instance.pk} to Firebase: {str(e)}")
            model_instance.sync_status = 'error'
            model_instance.save(update_fields=['sync_status'])
            return False

    def _get_collection_name_for_model(self, model_instance) -> Optional[str]:
        """Get Firebase collection name for a Django model instance"""
        model_class = type(model_instance)
        for collection_name, mapped_model in self.collection_model_mapping.items():
            if mapped_model == model_class:
                return collection_name
        return None

    def _model_to_firebase_data(self, model_instance) -> Dict[str, Any]:
        """Convert Django model instance to Firebase document data"""
        if isinstance(model_instance, Person):
            return {
                'national_id': model_instance.national_id,
                'name': model_instance.name,
                'debts_amount_2024': str(float(model_instance.debts_amount_2024))
            }
        elif isinstance(model_instance, HallReservation):
            return {
                'id_number': model_instance.person.national_id,
                'full_name': model_instance.full_name,
                'phone_number': model_instance.phone_number,
                'event_type': model_instance.event_type,
                'start_date': model_instance.start_date.strftime('%Y-%m-%d'),
                'end_date': model_instance.end_date.strftime('%Y-%m-%d'),
                'reservation_status': 'confirmed' if model_instance.reservation_status == 'confirmed' else 'In Progress',
                'admin_created': model_instance.admin_created,
                'created_at': model_instance.created_at.strftime('%Y-%m-%d %H:%M:%S')
            }
        # Add other model mappings as needed
        return {}

    def _generate_firebase_doc_id(self, model_instance) -> str:
        """Generate Firebase document ID for a model instance"""
        if isinstance(model_instance, Person):
            return model_instance.national_id
        elif isinstance(model_instance, HallReservation):
            return f"{model_instance.person.national_id}_{model_instance.created_at.strftime('%Y-%m-%d %H:%M:%S')}"
        else:
            return str(model_instance.pk)

    def get_sync_status(self, collection_name: str) -> Dict[str, Any]:
        """Get synchronization status for a collection"""
        model_class = self.collection_model_mapping.get(collection_name)
        if not model_class:
            return {'error': f'No model found for collection: {collection_name}'}

        total_records = model_class.objects.count()
        synced_records = model_class.objects.filter(sync_status='synced').count()
        pending_records = model_class.objects.filter(sync_status='pending').count()
        error_records = model_class.objects.filter(sync_status='error').count()

        # Get latest sync log
        latest_sync = FirebaseSyncLog.objects.filter(
            collection_name=collection_name
        ).order_by('-started_at').first()

        return {
            'collection_name': collection_name,
            'total_records': total_records,
            'synced_records': synced_records,
            'pending_records': pending_records,
            'error_records': error_records,
            'sync_percentage': (synced_records / total_records * 100) if total_records > 0 else 0,
            'latest_sync': {
                'status': latest_sync.sync_status if latest_sync else None,
                'started_at': latest_sync.started_at if latest_sync else None,
                'completed_at': latest_sync.completed_at if latest_sync else None,
                'records_processed': latest_sync.records_processed if latest_sync else 0,
                'success_rate': latest_sync.success_rate if latest_sync else 0
            } if latest_sync else None
        }
