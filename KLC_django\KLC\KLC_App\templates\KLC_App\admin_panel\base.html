{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>{% block title %}لوحة تحكم الإدارة{% endblock %} - مجلس كفر عين المحلي</title>
    
    <link rel="icon" type="image/x-icon" href="{% static 'images/logo.png' %}">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'primary': {
                            50: '#f0f4ff',
                            100: '#e0e7ff',
                            500: '#667eea',
                            600: '#5a67d8',
                            700: '#4c51bf',
                        },
                        'secondary': {
                            500: '#764ba2',
                            600: '#6b46c1',
                        }
                    }
                }
            }
        }
    </script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .hover-scale {
            transition: transform 0.2s ease;
        }
        
        .hover-scale:hover {
            transform: scale(1.02);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        
        .sidebar-gradient {
            background: linear-gradient(180deg, #2d3748 0%, #1a202c 100%);
        }
        
        .nav-link-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .nav-link-hover:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(-4px);
        }
        
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        .card-shadow:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>

<body class="gradient-bg min-h-screen">
    <!-- Mobile Menu Overlay -->
    <div id="mobile-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>
    
    <!-- Sidebar -->
    <aside id="sidebar" class="fixed top-0 right-0 z-50 w-64 h-screen sidebar-gradient sidebar-transition transform translate-x-full lg:translate-x-0">
        <div class="flex flex-col h-full">
            <!-- Logo and Title -->
            <div class="flex items-center justify-between p-6 border-b border-gray-600">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <img src="{% static 'images/logo.png' %}" alt="شعار المجلس" class="h-10 w-10">
                    <div>
                        <h1 class="text-white font-bold text-lg">لوحة الإدارة</h1>
                        <p class="text-gray-300 text-sm">مجلس كفر عين المحلي</p>
                    </div>
                </div>
                <button id="close-sidebar" class="lg:hidden text-white hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- Admin Info -->
            <div class="p-4 border-b border-gray-600">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="w-10 h-10 bg-primary-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div>
                        <p class="text-white font-medium">{{ admin_full_name }}</p>
                        <p class="text-gray-300 text-sm">{{ admin_profile.get_role_display }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Navigation -->
            <nav class="flex-1 p-4 space-y-2">
                <a href="{% url 'admin_panel_dashboard' %}" 
                   class="flex items-center space-x-3 space-x-reverse p-3 rounded-lg nav-link-hover transition-all duration-200 {% if active_page == 'dashboard' %}nav-link-active{% else %}text-gray-300{% endif %}">
                    <i class="fas fa-chart-line w-5"></i>
                    <span>الرئيسية</span>
                </a>
                
                {% if admin_permissions.manage_users %}
                <a href="{% url 'admin_user_management' %}" 
                   class="flex items-center space-x-3 space-x-reverse p-3 rounded-lg nav-link-hover transition-all duration-200 {% if active_page == 'user_management' %}nav-link-active{% else %}text-gray-300{% endif %}">
                    <i class="fas fa-users w-5"></i>
                    <span>إدارة المستخدمين</span>
                </a>
                {% endif %}
                
                {% if admin_permissions.approve_reservations %}
                <a href="{% url 'admin_reservation_management' %}" 
                   class="flex items-center space-x-3 space-x-reverse p-3 rounded-lg nav-link-hover transition-all duration-200 {% if active_page == 'reservation_management' %}nav-link-active{% else %}text-gray-300{% endif %}">
                    <i class="fas fa-calendar-alt w-5"></i>
                    <span>إدارة الحجوزات</span>
                </a>
                {% endif %}
                
                {% if admin_permissions.manage_transactions %}
                <a href="{% url 'admin_transaction_management' %}" 
                   class="flex items-center space-x-3 space-x-reverse p-3 rounded-lg nav-link-hover transition-all duration-200 {% if active_page == 'transaction_management' %}nav-link-active{% else %}text-gray-300{% endif %}">
                    <i class="fas fa-exchange-alt w-5"></i>
                    <span>إدارة المعاملات</span>
                </a>
                {% endif %}
                
                {% if admin_permissions.handle_complaints %}
                <a href="{% url 'admin_complaint_management' %}" 
                   class="flex items-center space-x-3 space-x-reverse p-3 rounded-lg nav-link-hover transition-all duration-200 {% if active_page == 'complaint_management' %}nav-link-active{% else %}text-gray-300{% endif %}">
                    <i class="fas fa-comment-alt w-5"></i>
                    <span>الشكاوي والاقتراحات</span>
                </a>
                {% endif %}
                
                {% if admin_permissions.manage_news %}
                <a href="{% url 'admin_news_management' %}" 
                   class="flex items-center space-x-3 space-x-reverse p-3 rounded-lg nav-link-hover transition-all duration-200 {% if active_page == 'news_management' %}nav-link-active{% else %}text-gray-300{% endif %}">
                    <i class="fas fa-newspaper w-5"></i>
                    <span>إدارة الأخبار</span>
                </a>
                {% endif %}
                
                {% if admin_permissions.view_reports %}
                <a href="{% url 'admin_reports' %}" 
                   class="flex items-center space-x-3 space-x-reverse p-3 rounded-lg nav-link-hover transition-all duration-200 {% if active_page == 'reports' %}nav-link-active{% else %}text-gray-300{% endif %}">
                    <i class="fas fa-chart-bar w-5"></i>
                    <span>التقارير والإحصائيات</span>
                </a>
                {% endif %}
                
                {% if admin_permissions.manage_admins %}
                <a href="{% url 'admin_management' %}" 
                   class="flex items-center space-x-3 space-x-reverse p-3 rounded-lg nav-link-hover transition-all duration-200 {% if active_page == 'admin_management' %}nav-link-active{% else %}text-gray-300{% endif %}">
                    <i class="fas fa-user-shield w-5"></i>
                    <span>إدارة المشرفين</span>
                </a>
                {% endif %}
            </nav>
            
            <!-- Logout -->
            <div class="p-4 border-t border-gray-600">
                <a href="{% url 'admin_logout_view' %}" 
                   class="flex items-center space-x-3 space-x-reverse p-3 rounded-lg text-gray-300 hover:bg-red-600 hover:text-white transition-all duration-200">
                    <i class="fas fa-sign-out-alt w-5"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </div>
        </div>
    </aside>
    
    <!-- Main Content -->
    <main class="lg:mr-64 min-h-screen">
        <!-- Top Bar -->
        <header class="bg-white shadow-sm border-b border-gray-200 p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <button id="menu-toggle" class="lg:hidden text-gray-600 hover:text-gray-900">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <h2 class="text-xl font-semibold text-gray-800">{% block page_title %}لوحة التحكم{% endblock %}</h2>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- Notifications -->
                    <button class="relative text-gray-600 hover:text-gray-900">
                        <i class="fas fa-bell text-xl"></i>
                        <span class="absolute -top-1 -left-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                    </button>
                    
                    <!-- User Menu -->
                    <div class="relative">
                        <button class="flex items-center space-x-2 space-x-reverse text-gray-600 hover:text-gray-900">
                            <div class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <span class="hidden md:block">{{ admin_full_name }}</span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Messages -->
        {% if messages %}
        <div class="p-4">
            {% for message in messages %}
            <div class="mb-4 p-4 rounded-lg border {% if message.tags == 'error' %}bg-red-50 border-red-200 text-red-700{% elif message.tags == 'success' %}bg-green-50 border-green-200 text-green-700{% elif message.tags == 'warning' %}bg-yellow-50 border-yellow-200 text-yellow-700{% else %}bg-blue-50 border-blue-200 text-blue-700{% endif %}">
                <div class="flex items-center">
                    <i class="fas {% if message.tags == 'error' %}fa-exclamation-triangle{% elif message.tags == 'success' %}fa-check-circle{% elif message.tags == 'warning' %}fa-exclamation-circle{% else %}fa-info-circle{% endif %} ml-2"></i>
                    {{ message }}
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <!-- Page Content -->
        <div class="p-6">
            {% block content %}{% endblock %}
        </div>
    </main>
    
    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        const menuToggle = document.getElementById('menu-toggle');
        const closeSidebar = document.getElementById('close-sidebar');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobile-overlay');
        
        function openSidebar() {
            sidebar.classList.remove('translate-x-full');
            overlay.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        }
        
        function closeSidebarFunc() {
            sidebar.classList.add('translate-x-full');
            overlay.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }
        
        menuToggle?.addEventListener('click', openSidebar);
        closeSidebar?.addEventListener('click', closeSidebarFunc);
        overlay?.addEventListener('click', closeSidebarFunc);
        
        // Auto-hide messages
        setTimeout(() => {
            const messages = document.querySelectorAll('[class*="bg-red-50"], [class*="bg-green-50"], [class*="bg-yellow-50"], [class*="bg-blue-50"]');
            messages.forEach(message => {
                message.style.transition = 'opacity 0.5s ease';
                message.style.opacity = '0';
                setTimeout(() => message.remove(), 500);
            });
        }, 5000);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
